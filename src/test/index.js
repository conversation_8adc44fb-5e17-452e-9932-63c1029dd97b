const postcss = require('postcss');
const cssnano = require('cssnano');
const { calculate, compare, compareDesc } = require('specificity');

const css = `
#app { width: 100px; background-color: aquamarine; color: blueviolet !important; }
div#app { background-color: chartreuse; }
`;

postcss([cssnano()])
  .process(css, { from: 'input.css', to: 'output.css' })
  .then(result => {
    console.log(result.css); // 输出优化后的 CSS
  });

const specificity = calculate('*');

const xx = compare(calculate('.classname'), calculate('div#app'));

const result = [
  'element',
  '#id',
  '.classname',
]
  .map((item, index) => ({ ...calculate(item), index }))
  .sort(compare);

console.log(specificity, result);
