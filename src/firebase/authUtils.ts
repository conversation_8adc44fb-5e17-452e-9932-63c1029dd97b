import { sendToBackground } from "@plasmohq/messaging"
import { signInWithCredential, GoogleAuthProvider, GithubAuthProvider } from "firebase/auth"
import { auth } from "./firebaseClient"
import type { AuthProviderType } from "@/background/auth/types"

// 通用的认证状态检查
export const checkAuthStatus = async (provider: AuthProviderType): Promise<{status: string, error?: string}> => {
  try {
    const response = await sendToBackground({
      name: "checkAuthStatus",
      body: { provider }
    })
    return response || { status: 'idle' }
  } catch (error: any) {
    console.error(`检查 ${provider} 认证状态失败:`, error)
    return { status: 'idle' }
  }
}

// 通用的认证请求
export const requestAuthFromBackground = async (provider: AuthProviderType): Promise<void> => {
  try {
    const displayName = provider === 'google' ? 'Google' : 'GitHub'
    console.log(`向 background script 请求 ${displayName} 认证...`)

    const response = await sendToBackground({
      name: "auth",
      body: { provider }
    })

    if (response.success) {
      console.log(`Background script ${displayName} 认证流程启动成功`)
    } else {
      console.error(`Background script ${displayName} 认证失败:`, response.error)
      throw new Error(response.error || `${displayName} 认证失败`)
    }
  } catch (error: any) {
    console.error(`${provider} 消息发送失败:`, error)
    throw new Error(error.message || `${provider} 消息发送失败`)
  }
}

// 通用的待处理令牌获取
export const getPendingToken = (provider: AuthProviderType): Promise<string | null> => {
  return new Promise((resolve) => {
    const displayName = provider === 'google' ? 'Google' : 'GitHub'
    const storageKeys = provider === 'google'
      ? { pendingToken: 'pendingIdToken', tokenTimestamp: 'idTokenTimestamp' }
      : { pendingToken: 'pendingGithubToken', tokenTimestamp: 'githubTokenTimestamp' }

    chrome.storage.local.get([
      storageKeys.pendingToken,
      storageKeys.tokenTimestamp
    ], (result) => {
      if (chrome.runtime.lastError) {
        console.error(`获取待处理 ${displayName} 令牌失败:`, chrome.runtime.lastError)
        resolve(null)
        return
      }

      const token = result[storageKeys.pendingToken]
      const timestamp = result[storageKeys.tokenTimestamp]

      // 检查 token 是否存在且未过期（5分钟内有效）
      if (token && timestamp) {
        const now = Date.now()
        const tokenAge = now - timestamp
        const maxAge = 5 * 60 * 1000 // 5分钟

        if (tokenAge < maxAge) {
          resolve(token)
        } else {
          console.log(`${displayName} 令牌已过期，清除`)
          chrome.storage.local.remove([
            storageKeys.pendingToken,
            storageKeys.tokenTimestamp
          ])
          resolve(null)
        }
      } else {
        resolve(null)
      }
    })
  })
}

// 通用的待处理令牌清除
export const clearPendingToken = (provider: AuthProviderType): Promise<void> => {
  return new Promise((resolve) => {
    const storageKeys = provider === 'google'
      ? { pendingToken: 'pendingIdToken', tokenTimestamp: 'idTokenTimestamp' }
      : { pendingToken: 'pendingGithubToken', tokenTimestamp: 'githubTokenTimestamp' }

    chrome.storage.local.remove([
      storageKeys.pendingToken,
      storageKeys.tokenTimestamp
    ], () => {
      resolve()
    })
  })
}

// 通用的认证状态清除
export const clearAuthStatus = async (provider: AuthProviderType): Promise<void> => {
  try {
    await sendToBackground({
      name: "clearAuthStatus",
      body: { provider }
    })
  } catch (error: any) {
    console.error(`清除 ${provider} 认证状态失败:`, error)
  }
}

// 通用的 Firebase 认证函数
export const signInWithProvider = async (provider: AuthProviderType, token: string) => {
  try {
    const displayName = provider === 'google' ? 'Google' : 'GitHub'
    console.log(`使用 ${displayName} 令牌进行 Firebase 认证...`)

    let credential
    if (provider === 'google') {
      // Google 使用 ID Token
      credential = GoogleAuthProvider.credential(token, null)
    } else if (provider === 'github') {
      // GitHub 使用 Access Token
      credential = GithubAuthProvider.credential(token)
    } else {
      throw new Error(`不支持的认证提供商: ${provider}`)
    }

    // 使用凭据登录 Firebase
    const result = await signInWithCredential(auth, credential)
    console.log(`${displayName} 登录成功:`, result.user.email)

    return result.user
  } catch (error: any) {
    const displayName = provider === 'google' ? 'Google' : 'GitHub'
    console.error(`${displayName} 登录失败:`, error)
    throw new Error(`${displayName} 登录失败: ` + error.message)
  }
}

// 通用的退出登录函数
export const signOutProvider = async (provider: AuthProviderType) => {
  try {
    const displayName = provider === 'google' ? 'Google' : 'GitHub'
    console.log(`${displayName} 登录退出（launchWebAuthFlow 无需特殊处理）`)
  } catch (error) {
    const displayName = provider === 'google' ? 'Google' : 'GitHub'
    console.error(`${displayName} 退出登录处理失败:`, error)
  }
}


