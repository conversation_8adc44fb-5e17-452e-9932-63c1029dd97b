import React from "react"
import UserAvatar from "@/components/auth/UserAvatar"
import AuthForm from "@/components/auth/AuthForm"
import { FirebaseAuthProvider, useFirebaseAuth } from "@/contexts/FirebaseAuthContext"

import "./style.css"

function OptionsContent() {
  const { user, isLoading } = useFirebaseAuth()

  return (
    <div className="home-container">
      {/* 顶部导航栏 */}
      <header className="home-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="site-title">Firebase Auth Demo</h1>
          </div>
          <div className="header-right">
            <UserAvatar />
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="home-main">
        {isLoading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <div className="loading-text">加载中...</div>
          </div>
        )}

        {!isLoading && !user && (
          <div className="welcome-section">
            <div className="welcome-content">
              <h2>欢迎使用 Firebase Auth</h2>
              <p>请登录以继续使用应用</p>
              <div className="auth-form-container">
                <AuthForm />
              </div>
            </div>
          </div>
        )}

        {!isLoading && user && (
          <div className="dashboard-section">
            <div className="dashboard-content">
              <h2>欢迎回来，{user.displayName || user.email?.split('@')[0]}！</h2>
              <p>您已成功登录到应用</p>

              <div className="dashboard-cards">
                <div className="dashboard-card">
                  <h3>用户信息</h3>
                  <div className="card-content">
                    <p><strong>邮箱:</strong> {user.email}</p>
                    <p><strong>用户ID:</strong> {user.uid.substring(0, 16)}...</p>
                    <p><strong>创建时间:</strong> {new Date(user.metadata.creationTime!).toLocaleDateString()}</p>
                    <p><strong>最后登录:</strong> {new Date(user.metadata.lastSignInTime!).toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="dashboard-card">
                  <h3>账户状态</h3>
                  <div className="card-content">
                    <div className="status-item">
                      <span className="status-label">邮箱验证:</span>
                      <span className={`status-value ${user.emailVerified ? 'verified' : 'not-verified'}`}>
                        {user.emailVerified ? '✅ 已验证' : '⚠️ 未验证'}
                      </span>
                    </div>
                    <div className="status-item">
                      <span className="status-label">登录方式:</span>
                      <span className="status-value">
                        {user.providerData.map((provider, index) => (
                          <span key={index} className="provider-badge">
                            {provider.providerId === 'google.com' && '🔍 Google'}
                            {provider.providerId === 'github.com' && '🐙 GitHub'}
                            {provider.providerId === 'password' && '📧 邮箱密码'}
                          </span>
                        ))}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="dashboard-card">
                  <h3>快速操作</h3>
                  <div className="card-content">
                    <p>这里可以添加一些快速操作按钮或功能</p>
                    <div className="quick-actions">
                      <button className="action-btn" onClick={() => window.open('https://console.firebase.google.com/project/css-cliper/authentication/users', '_blank')}>
                        Firebase 控制台
                      </button>
                      <button className="action-btn" onClick={() => window.open('https://github.com/settings/applications/3104677', '_blank')}>
                        GitHub
                      </button>
                      <button className="action-btn" onClick={() => window.open('https://console.cloud.google.com/auth/clients/815018773432-tder8h7gvkalk5lr5ui7mgtdnvsrulvi.apps.googleusercontent.com?inv=1&invt=Ab4NXQ&project=css-cliper', '_blank')}>
                        Google
                      </button>
                    </div>
                  </div>
                </div>

                <div className="dashboard-card">
                  <h3>今日信息</h3>
                  <div className="card-content">
                    <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
                    <p><strong>今日日期:</strong> {new Date().toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      weekday: 'long'
                    })}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* 页脚 */}
      <footer className="home-footer">
        <div className="footer-content">
          <p>&copy; 2024 Firebase Auth Demo. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

export default function OptionsPage() {
  return (
    <FirebaseAuthProvider>
      <OptionsContent />
    </FirebaseAuthProvider>
  )
}
