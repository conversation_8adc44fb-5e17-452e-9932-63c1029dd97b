/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

img {
  -webkit-user-drag: none;
}

/* 弹窗容器 */
.popup-container {
  width: 350px;
  max-height: 580px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.popup-header {
  background: #4285f4;
  color: white;
  padding: 12px;
  text-align: center;
  border-radius: 8px 8px 0 0;
}

.popup-header h1 {
  font-size: 18px;
  font-weight: 600;
}

.popup-content {
  padding: 12px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading {
  color: #666;
  font-size: 16px;
}

/* 认证表单 */
.auth-container {
  width: 100%;
}

.auth-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.tab-button {
  flex: 1;
  padding: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button.active {
  color: #4285f4;
  border-bottom-color: #4285f4;
  font-weight: 500;
}

.tab-button:hover {
  background-color: #f5f5f5;
}

.auth-form {
  margin-bottom: 12px;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.form-group input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 12px;
  font-size: 13px;
  border: 1px solid #fecaca;
}

.submit-button {
  width: 100%;
  padding: 10px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  background-color: #3367d6;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
}

.auth-footer p {
  color: #666;
  font-size: 13px;
}

.link-button {
  background: none;
  border: none;
  color: #4285f4;
  cursor: pointer;
  text-decoration: underline;
  font-size: 13px;
  margin-left: 4px;
}

.link-button:hover:not(:disabled) {
  color: #3367d6;
}

.link-button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* 用户信息 */
.user-info {
  text-align: center;
}

.welcome-message {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.welcome-message h3 {
  color: #333;
  margin-bottom: 8px;
  font-size: 16px;
}

.welcome-message p {
  color: #666;
  margin-bottom: 6px;
  font-size: 13px;
  word-break: break-all;
}

.welcome-message p:last-child {
  margin-bottom: 0;
}

.logout-button {
  width: 100%;
  padding: 10px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.logout-button:hover {
  background-color: #c82333;
}

/* 邮箱验证状态 */
.email-verification-status {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.verified {
  color: #28a745;
  font-weight: 500;
  margin: 0;
}

.not-verified {
  text-align: center;
}

.warning {
  color: #ffc107;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.verify-button {
  width: 100%;
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.verify-button:hover {
  background-color: #218838;
}

/* 分隔线 */
.divider {
  display: flex;
  align-items: center;
  margin: 12px 0;
  text-align: center;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #e0e0e0;
}

.divider span {
  padding: 0 12px;
  color: #666;
  font-size: 14px;
}

/* Google 登录按钮 */
.google-login-button {
  width: 100%;
  padding: 10px 12px;
  background-color: #ffffff;
  color: #333;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.google-login-button:hover {
  background-color: #f8f9fa;
  border-color: #dadce0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.google-login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.google-icon {
  flex-shrink: 0;
}

/* GitHub 登录按钮 */
.github-login-button {
  width: 100%;
  padding: 10px 12px;
  background-color: #ffffff;
  color: #333;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.github-login-button:hover {
  background-color: #f8f9fa;
  border-color: #dadce0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.github-login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.github-icon {
  flex-shrink: 0;
}

/* UserProfile 组件样式 */
.user-profile {
  text-align: center;
}

.user-profile h3 {
  color: #333;
  margin-bottom: 12px;
  font-size: 18px;
}

.user-profile .user-info {
  margin-bottom: 16px;
  text-align: left;
}

.user-profile .user-info p {
  color: #666;
  margin-bottom: 6px;
  font-size: 14px;
}

.user-profile .user-info strong {
  color: #333;
}

.verification-status {
  margin: 8px 0;
  padding: 6px;
  border-radius: 4px;
  text-align: center;
}

.provider-info {
  margin-top: 8px;
}

.provider-info p {
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.provider-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.provider-info li {
  padding: 2px 0;
  color: #666;
}

.actions {
  margin-top: 12px;
}

.logout-btn {
  background: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background: #d32f2f;
}

/* 主页样式 */
.home-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.home-header {
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.site-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 主要内容区域 */
.home-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  width: 100%;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 60px 0;
}

.welcome-content h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 16px;
}

.welcome-content p {
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
}

.auth-form-container {
  max-width: 400px;
  margin: 0 auto;
  background: #ffffff;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 仪表板区域 */
.dashboard-section {
  padding: 20px 0;
}

.dashboard-content h2 {
  font-size: 28px;
  color: #333;
  margin-bottom: 8px;
}

.dashboard-content > p {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.dashboard-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.dashboard-card h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #4285f4;
  padding-bottom: 8px;
}

.card-content p {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.card-content strong {
  color: #333;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.status-label {
  font-weight: 500;
  color: #333;
}

.status-value {
  font-size: 14px;
}

.status-value.verified {
  color: #28a745;
}

.status-value.not-verified {
  color: #ffc107;
}

.provider-badge {
  display: inline-block;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.quick-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 8px 16px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #3367d6;
}

/* 页脚 */
.home-footer {
  background: #ffffff;
  border-top: 1px solid #e9ecef;
  padding: 20px 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.footer-content p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 用户头像组件样式 */
.user-avatar-container {
  position: relative;
  display: inline-block;
}

.user-avatar {
  display: flex;
  align-items: center;
  padding: 4px;
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  background: rgba(66, 133, 244, 0.1);
  transform: scale(1.05);
}

.avatar-image {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.user-avatar:hover .avatar-image {
  border-color: #4285f4;
}

.avatar-initials {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #4285f4;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.user-avatar:hover .avatar-initials {
  background-color: #3367d6;
}



.login-prompt-btn {
  padding: 8px 16px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-prompt-btn:hover {
  background: #3367d6;
}

/* 下拉菜单样式 */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.user-info-compact {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e9ecef;
}

.dropdown-avatar-initials {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #4285f4;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-divider {
  height: 1px;
  background: #e9ecef;
  margin: 0;
}

.dropdown-content {
  padding: 16px;
}

.user-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.user-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  font-weight: 500;
}

.info-value {
  color: #333;
  text-align: right;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-value.verified {
  color: #28a745;
}

.info-value.not-verified {
  color: #ffc107;
}

.dropdown-actions {
  padding: 16px;
  background: #f8f9fa;
}

.dropdown-logout-btn {
  width: 100%;
  padding: 10px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-logout-btn:hover {
  background: #c82333;
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 16px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
    height: 56px;
  }

  .site-title {
    font-size: 20px;
  }

  .home-main {
    padding: 20px 16px;
  }

  .welcome-content h2 {
    font-size: 24px;
  }

  .welcome-content p {
    font-size: 16px;
  }

  .auth-form-container {
    padding: 24px;
    margin: 0 16px;
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .dashboard-card {
    padding: 20px;
  }

  .user-dropdown {
    min-width: 260px;
    right: -20px;
  }

  .quick-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
  }

  .site-title {
    font-size: 18px;
  }

  .user-avatar {
    padding: 6px 10px;
  }

  .avatar-image,
  .avatar-initials {
    width: 28px;
    height: 28px;
  }

  .dropdown-avatar,
  .dropdown-avatar-initials {
    width: 36px;
    height: 36px;
  }

  .user-dropdown {
    min-width: 240px;
    right: -40px;
  }

  .welcome-content {
    padding: 40px 0;
  }

  .welcome-content h2 {
    font-size: 20px;
  }

  .dashboard-content h2 {
    font-size: 24px;
  }
}
