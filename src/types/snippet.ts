export interface CodeSnippet {
  id: string;
  title: string;
  html: string;
  css: string;
  url: string;
  selector?: string;
  createdAt: number;
  updatedAt: number;
  tags?: string[];
  description?: string;
  thumbnail?: string;
  domInfo?: any; // Raw DOM tree info from buildDomTree
}

export interface StorageData {
  snippets: CodeSnippet[];
  lastUpdated: number;
}

export interface SnippetCardProps {
  snippet: CodeSnippet;
  onDelete: (snippet: CodeSnippet) => void;
  onUpdate: (id: string, updates: Partial<CodeSnippet>) => void;
}

export interface ConfirmDialogProps {
  show: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

export interface StorageUtils {
  getStoredSnippets: () => Promise<CodeSnippet[]>;
  saveSnippet: (snippet: Omit<CodeSnippet, 'id' | 'createdAt' | 'updatedAt'>) => Promise<CodeSnippet>;
  updateSnippet: (id: string, updates: Partial<CodeSnippet>) => Promise<CodeSnippet>;
  deleteSnippet: (id: string) => Promise<void>;
  clearAllSnippets: () => Promise<void>;
}
