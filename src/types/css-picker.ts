/**
 * Type definitions for CSS Picker utility
 */

export interface InheritMap {
  all: string[];
  text: string[];
  block: string[];
  list: string[];
  table: string[];
}

export interface TagWithResource {
  tag: string;
  attr: string;
}

export interface StyleMap {
  [property: string]: string;
}

export interface AttributeMap {
  [attribute: string]: string;
}

export interface CSSRuleInfo {
  cssText: string;
  selectorText: string;
  media?: string;
  styles?: StyleMap;
  importants?: StyleMap;
  origin?: string;
}

export interface StyleSheets {
  middle: StyleMap;
  after: CSSRuleInfo[];
  before: CSSRuleInfo[];
  keyframes?: string[];
  allMatchedRules?: { [key: string]: CSSRuleInfo };
  debuggerStyles?: any;
}

export interface PseudoElementStyles {
  styleSheets: StyleMap;
  computedStyles: StyleMap;
}

export interface DomTreeNode {
  tagName: string;
  computedStyles: StyleMap;
  attributes: AttributeMap;
  styleSheets: StyleMap;
  after: PseudoElementStyles;
  before: PseudoElementStyles;
  children?: (DomTreeNode | string)[];
  rect?: DOMRect;
  keyframes?: string[];
  usedRules?: { [key: string]: CSSRuleInfo };
  innerHTML?: string;
}

export interface GlobalStyles {
  rules: CSSRuleInfo[];
  keyframes: { [name: string]: string };
  usedKeyframes: string[];
  usedRules: { [key: string]: CSSRuleInfo };
  useDebugger: boolean;
  debuggerExtractor: any; // DebuggerStyleExtractor type
}

export interface RenderPayload {
  svg: Element | null;
  stylesheet: { [key: string]: any };
  keyframes: string[] | null;
}

export interface RenderOptions {
  svgUseReplace?: boolean;
}

export interface GeneratedCode {
  css: string;
  html: string;
}

export interface SvgSymbolInfo {
  viewBox?: string;
  id: string;
  innerHTML?: string;
  parent: {
    tagName: string;
    attributes: AttributeMap;
  };
}

export interface ChromeTabInfo {
  id?: number;
  url?: string;
  title?: string;
}

export interface BackgroundResponse {
  tab?: ChromeTabInfo;
  success?: boolean;
  error?: string;
  result?: any;
}

export interface AtomRulesResult {
  keyframes: { [name: string]: string };
  rules: CSSRuleInfo[];
}

export interface SpecificityResult {
  index: number;
}

export interface RuleStylesResult {
  styles: StyleMap;
  importants: StyleMap;
  keyframes: string[];
}
