import { calculate, compare } from 'specificity';
import { parse } from 'postcss';
import * as selectorParser from 'postcss-selector-parser';
import { getDebuggerExtractor, DebuggerStyleExtractor, cleanupDebuggerExtractor } from './debugger-styles';
import { sendToBackground } from "@plasmohq/messaging";
import type {
  InheritMap,
  TagWithResource,
  StyleMap,
  AttributeMap,
  CSSRuleInfo,
  StyleSheets,
  PseudoElementStyles,
  DomTreeNode,
  GlobalStyles,
  RenderPayload,
  RenderOptions,
  GeneratedCode,
  SvgSymbolInfo,
  ChromeTabInfo,
  BackgroundResponse,
  AtomRulesResult,
  SpecificityResult,
  RuleStylesResult
} from '../types/css-picker';

// 常量定义
const inheritMap: InheritMap = {
  all: ['visibility', 'cursor'],
  text: ['letter-spacing', 'word-spacing', 'white-space', 'line-height', 'color', 'font', 'font-family', 'font-size', 'font-style', 'font-variant', 'font-weight', 'text-decoration', 'text-transform', 'direction'],
  block: ['text-indent', 'text-align'],
  list: ['list-style', 'list-style-type', 'list-style-position', 'list-style-image'],
  table: ['border-collapse']
};

const tagWithResources: TagWithResource[] = [
  { tag: 'A', attr: 'href' },
  { tag: 'IMG', attr: 'src' },
  { tag: 'AUDIO', attr: 'src' },
  { tag: 'VIDEO', attr: 'src' },
  { tag: 'IFRAME', attr: 'src' },
  { tag: 'EMBED', attr: 'src' },
  { tag: 'OBJECT', attr: 'data' },
  { tag: 'SOURCE', attr: 'src' },
  { tag: 'TRACE', attr: 'src' }
];

// 获取当前标签页
async function getCurrentTab(): Promise<ChromeTabInfo | null> {
  try {
    // 在内容脚本中，我们无法直接访问 chrome.tabs API
    // 所以我们通过后台脚本来获取当前标签页信息
    const response: BackgroundResponse = await sendToBackground({
      name: "getCurrentTab"
    });
    return response?.tab || null;
  } catch (error) {
    console.error('获取当前标签页失败:', error);
    return null;
  }
}

// 递归函数，用于构建 DOM 树结构并输出属性和标签树
export async function buildDomTree(
  element: Element,
  indexList: number[] = [1],
  global: GlobalStyles = {
    rules: [],
    keyframes: {},
    usedKeyframes: [],
    usedRules: {},
    useDebugger: true,
    debuggerExtractor: null
  }
): Promise<DomTreeNode> {
  if (!element.tagName) {
    return {
      tagName: '',
      computedStyles: {},
      attributes: {},
      styleSheets: {},
      after: {
        styleSheets: {},
        computedStyles: {}
      },
      before: {
        styleSheets: {},
        computedStyles: {}
      }
    };
  }

  const tagName = element.tagName.toLowerCase();
  const node: DomTreeNode = {
    tagName,
    computedStyles: {},
    attributes: {},
    styleSheets: {},
    after: {
      styleSheets: {},
      computedStyles: {}
    },
    before: {
      styleSheets: {},
      computedStyles: {}
    }
  };

  if (indexList.length === 1) {
    global = getGlobalStyles();
    node.rect = element.getBoundingClientRect();

    // 初始化调试器（如果启用）
    if (global.useDebugger) {
      try {
        global.debuggerExtractor = getDebuggerExtractor();
        const currentTab = await getCurrentTab();
        if (currentTab?.id) {
          await global.debuggerExtractor.attach(currentTab.id);
        }
      } catch (error) {
        console.warn('初始化调试器失败，回退到传统方法:', error);
        global.useDebugger = false;
      }
    }
  }

  const styleSheets = await getStyleSheets(element, global);
  node.styleSheets = styleSheets.middle;
  node.after.styleSheets = styleSheets.after.reduce((acc, rule) => ({ ...acc, ...rule.styles }), {});
  node.before.styleSheets = styleSheets.before.reduce((acc, rule) => ({ ...acc, ...rule.styles }), {});
  global.usedRules = {
    ...(global.usedRules || {}),
    ...styleSheets.allMatchedRules
  };

  // 获取元素的计算样式
  const styles = window.getComputedStyle(element);
  const afterStyles = window.getComputedStyle(element, '::after');
  const beforeStyles = window.getComputedStyle(element, '::before');
  node.after.computedStyles = getStyleMap(afterStyles);
  node.before.computedStyles = getStyleMap(beforeStyles);
  const tagInitStyles = getInitStyle(tagName, styleSheets, indexList);

  // 获取并存储元素的样式信息
  for (let i = 0; i < styles.length; i++) {
    const prop = styles[i];
    const value = styles.getPropertyValue(prop);

    if (tagInitStyles[prop] !== value) {
      node.computedStyles[prop] = value;
    }
  }

  // 获取并存储元素的属性信息
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i];
    if (['style', 'id', 'class'].includes(attr.name)) continue;

    for (let item of tagWithResources) {
      if (element.tagName === item.tag && attr.name === item.attr) {
        attr.value = (element as any)[attr.name];
        break;
      }
    }

    node.attributes[attr.name] = attr.value;
  }

  // 递归遍历子节点
  for (let i = 0; i < element.childNodes.length; i++) {
    const child = element.childNodes[i];

    if (child instanceof Text) {
      if (child.data.trim() === '') continue;
      node.children = node.children || [];
      node.children.push(child.data);
    } else if (child instanceof Element) {
      const childNode = await buildDomTree(child, [...indexList, i + 1], global);
      node.children = node.children || [];
      node.children.push(childNode);
    }
  }

  if (indexList.length === 1) {
    node.keyframes = global.usedKeyframes?.map(name => global.keyframes[name]);
    node.usedRules = global.usedRules;

    // 清理调试器连接
    if (global.useDebugger && global.debuggerExtractor) {
      try {
        await global.debuggerExtractor.detach();
      } catch (error) {
        console.warn('分离调试器失败:', error);
      }
    }
  }

  return node;
}

function splitSelectorText(selectorText: string): string[] {
  const result: string[] = [];

  // @ts-ignore
  selectorParser((selectors) => {
    selectors.each((selector) => {
      result.push(selector.toString());
    });
  }).processSync(selectorText);

  return result;
}

function getGlobalStyles(): GlobalStyles {
  const pseudo = [':hover', ':focus', ':active', ':before', ':after'];

  let allRules: CSSRuleInfo[] = [];
  let keyframes: { [name: string]: string } = {};

  // 获取所有样式表
  const styleSheets = Array.from(document.styleSheets);

  styleSheets.forEach(function (sheet) {
    try {
      const result = atomRules(sheet.cssRules || sheet.rules);
      allRules = allRules.concat(result.rules);
      keyframes = {
        ...keyframes,
        ...result.keyframes
      };
    } catch (e) {
      console.error('Could not access CSS rules for style sheet ' + sheet.href, e);
    }
  });

  return {
    keyframes,
    usedKeyframes: [],
    rules: allRules,
    usedRules: {},
    useDebugger: true,
    debuggerExtractor: null
  };
}



function atomRules(cssRules: CSSRuleList, result: AtomRulesResult = { keyframes: {}, rules: [] }, media?: string): AtomRulesResult {
  // 获取样式表中的所有规则
  const rules = Array.from(cssRules);

  rules.forEach(rule => {
    switch (rule?.type) {
      case CSSRule.KEYFRAMES_RULE:
        // rule instanceof CSSKeyframesRule
        // 如果规则是@keyframes，添加到keyframes对象中
        const keyframesRule = rule as CSSKeyframesRule;
        result.keyframes[keyframesRule.name] = keyframesRule.cssText;
        break;
      case CSSRule.MEDIA_RULE:
        // rule instanceof CSSMediaRule
        // TODO:媒体查询
        const mediaRule = rule as CSSMediaRule;
        const { cssRules: mediaCssRules, conditionText } = mediaRule;

        atomRules(mediaCssRules, result, conditionText);
        break;
      case CSSRule.STYLE_RULE:
        // rule instanceof CSSStyleRule
        const styleRule = rule as CSSStyleRule;
        const selectors = splitSelectorText(styleRule.selectorText);

        selectors.forEach(selectorText => {
          result.rules.push({
            cssText: styleRule.cssText,
            selectorText,
            media
          });
        });
        break;
      default:
        console.log(rule, '0000000000');
        break;
    }
  });

  return result;
}

function getStyleMap(styles: CSSStyleDeclaration): StyleMap {
  const result: StyleMap = {};
  for (let i = 0; i < styles.length; i++) {
    const prop = styles[i];
    result[prop] = styles.getPropertyValue(prop);
  }
  return result;
}

function getAttributeMap(attributes: NamedNodeMap): AttributeMap {
  const map: AttributeMap = {};
  for (let i = 0; i < attributes.length; i++) {
    const attr = attributes[i];
    map[attr.name] = attr.value;
  }
  return map;
}

export function styleDict2StyleSheet(dict: StyleMap): string {
  return Object.entries(dict).map(([prop, value]) => {
    return `${prop}: ${value};`;
  }).join('\n');
}



function getInitStyle(tagName: string, { middle, before, after }: StyleSheets, indexList: number[]): StyleMap {
  let host = document.querySelector('shadow-root') as HTMLElement;

  if (!host) {
    host = document.createElement('shadow-root') as HTMLElement;
    document.documentElement.appendChild(host);
  }

  const root = host.shadowRoot || host.attachShadow({ mode: 'open' });

  // 创建一个新的元素
  const element = document.createElement(tagName);

  // 设置行内样式
  Object.entries(middle).map(([key, value]) => {
    (element.style as any)[key] = value;
  });

  root.appendChild(element);

  // 获取元素的所有计算后的样式
  const styles = window.getComputedStyle(element);
  const initStyleMap: StyleMap = {};

  for (let i = 0; i < styles.length; i++) {
    const prop = styles[i];
    initStyleMap[prop] = styles.getPropertyValue(prop);
  }

  element.remove();

  return initStyleMap;
}

async function getStyleSheets(element: Element, global: GlobalStyles): Promise<StyleSheets> {
  const keyframes: string[] = [];
  let allMatchedRules: { [key: string]: CSSRuleInfo } = {};
  let debuggerStyles: any = null;

  // 尝试使用 debugger API 获取样式
  if (global.useDebugger && global.debuggerExtractor) {
    try {
      debuggerStyles = await global.debuggerExtractor.getElementStyles(element);
      if (debuggerStyles.matchedStyles) {
        const convertedStyles = DebuggerStyleExtractor.convertMatchedStylesToOurFormat(debuggerStyles.matchedStyles);

        // 将 debugger 获取的样式转换为我们的格式
        convertedStyles.rules.forEach((rule, index) => {
          allMatchedRules[`debugger_${index}`] = {
            cssText: rule.cssText,
            selectorText: rule.selectorText,
            media: rule.media,
            styles: rule.styles,
            importants: rule.importants,
            origin: rule.origin
          };
        });

        // TODO: 处理继承样式
        // convertedStyles.inherited.forEach((inheritedItem, index) => {
        //   // 处理继承的样式
        // });

        console.log('Successfully got styles from debugger API');
      }
    } catch (error) {
      console.warn('Failed to get styles from debugger, falling back to traditional method:', error);
    }
  }

  // 如果 debugger 没有获取到样式，使用传统方法
  if (Object.keys(allMatchedRules).length === 0) {
    allMatchedRules = getAllMatchedRules(element, global);
  }

  const matchedSelectors = Object.values(allMatchedRules)?.map(item => item.selectorText);
  const sortIndex = sortSelector(matchedSelectors);

  let middle: StyleMap = {};
  let importants: StyleMap = {};

  sortIndex.forEach(index => {
    const { styles, importants: imps, media } = Object.values(allMatchedRules)[index];

    if (media) {
      // TODO: 媒体查询匹配样式处理
    }

    middle = {
      ...middle,
      ...styles
    };

    importants = {
      ...importants,
      ...imps
    };
  });

  const middleStyles = getRuleStyles(`.inline {\n${(element as HTMLElement).style.cssText}\n}`, global);
  global.usedKeyframes = [...global.usedKeyframes, ...middleStyles.keyframes];

  const vSelectors = matchedSelectors.reduce((result: string[], selector) => {
    return [...result, selector + '::after', selector + '::before'];
  }, []);

  let after: CSSRuleInfo[] = matchedSelectors.map(item => ({ cssText: '', selectorText: item + '::after' }));
  let before: CSSRuleInfo[] = matchedSelectors.map(item => ({ cssText: '', selectorText: item + '::before' }));

  // 处理伪元素样式
  if (debuggerStyles?.matchedStyles) {
    const convertedStyles = DebuggerStyleExtractor.convertMatchedStylesToOurFormat(debuggerStyles.matchedStyles);

    // 从 debugger 获取伪元素样式
    if (convertedStyles.pseudoElements['::after']) {
      after = convertedStyles.pseudoElements['::after'];
    } else {
      // 在所有样式表中查找可能存在的伪元素
      after = global.rules.filter(item => after.some(afterItem => afterItem.selectorText === item.selectorText));
    }

    if (convertedStyles.pseudoElements['::before']) {
      before = convertedStyles.pseudoElements['::before'];
    } else {
      before = global.rules.filter(item => before.some(beforeItem => beforeItem.selectorText === item.selectorText));
    }
  } else {
    // 传统方法查找伪元素
    after = global.rules.filter(item => after.some(afterItem => afterItem.selectorText === item.selectorText));
    before = global.rules.filter(item => before.some(beforeItem => beforeItem.selectorText === item.selectorText));
  }

  middle = {
    ...middle,
    ...middleStyles.styles,
    ...importants,
    ...middleStyles.importants
  };

  return {
    middle,
    after,
    before,
    keyframes,
    // 新增所有匹配的样式表，不包含内联样式
    allMatchedRules,
    // 新增 debugger 获取的原始数据
    debuggerStyles
  };
}

// 检查选择器是否为全局标签样式
function isGlobalTagStyle(selectorText: string): string | undefined {
  const regex = /^[a-zA-Z]+(?![^\s])/;

  if (regex.test(selectorText) || selectorText === '*') {
    return selectorText;
  }
}

function getAllMatchedRules(element: Element, global: GlobalStyles): { [key: string]: CSSRuleInfo } {
  const allMatchedRules: { [key: string]: CSSRuleInfo } = {};

  global.rules?.forEach(function (rule, index) {
    try {
      if (element.matches(rule.selectorText)) {
        const styles = getRuleStyles(rule.cssText, global);
        global.usedKeyframes = [...global.usedKeyframes, ...styles.keyframes];

        // 匹配上通用标签样式
        // const tag = isGlobalTagStyle(found);
        // TODO:通用样式提取
        // console.log(tag, 'xxxxxxxxxxxxxxxxx');

        allMatchedRules[index] = {
          cssText: rule.cssText,
          selectorText: rule.selectorText,
          media: rule.media,
          styles: styles.styles,
          importants: styles.importants,
        };
      }
    } catch (e) {
      console.error(e, rule, 'xxxxxxxxx');
    }
  });

  return allMatchedRules;
}

export function getSvgSymbol(idSelector: string): SvgSymbolInfo | null {
  const symbol = document.querySelector(`svg > symbol${idSelector}`) as SVGSymbolElement;
  if (!symbol) return null;

  const viewBox = symbol?.attributes?.getNamedItem('viewBox')?.value;
  const id = symbol.id;
  const innerHTML = symbol?.innerHTML;

  const attributes = getAttributeMap(symbol.parentElement!.attributes);
  const tagName = symbol.parentElement!.tagName.toLowerCase();

  return {
    viewBox,
    id,
    innerHTML,
    parent: {
      tagName,
      attributes
    }
  };
}



function sortSelector(selectors: string[]): number[] {
  return selectors
    ?.map((item, index) => ({ ...calculate(item), index }))
    .sort(compare)
    .map((item: SpecificityResult) => item.index);
}



function getRuleStyles(cssText: string, global: GlobalStyles = { rules: [], keyframes: {}, usedKeyframes: [], usedRules: {}, useDebugger: false, debuggerExtractor: null }): RuleStylesResult {
  const styleSheets = parse(cssText);
  const styles: StyleMap = {};
  const importants: StyleMap = {};
  const keyframes: string[] = [];

  // 遍历所有声明
  styleSheets.walkDecls(decl => {
    if (decl.prop === 'animation') {
      const found = Object.keys(global.keyframes).find(name => {
        return decl.value.includes(name);
      });
      found && keyframes.push(found);
    }

    if (decl.important) {
      importants[decl.prop] = decl.value;
    } else {
      styles[decl.prop] = decl.value;
    }
  });

  return {
    styles,
    importants,
    keyframes
  };
}

export function createNode(element: DomTreeNode): Element {
  const node = document.createElement(element.tagName);

  // 行内样式
  // element.styleSheets && Object.entries(element.styleSheets).map(([key, value]) => {
  //   node.style[key] = value;
  // });

  element.attributes && Object.entries(element.attributes).map(([key, value]) => {
    node.setAttribute(key, value);
  });

  if (element.innerHTML) {
    node.innerHTML = element.innerHTML;
  }

  return node;
}

function render(domInfo: DomTreeNode | string, payload: RenderPayload, options: RenderOptions = {}, indexList: number[] = [1]): Node {
  if (indexList.length === 1 && typeof domInfo !== 'string') {
    payload.keyframes = domInfo.keyframes || null;
  }

  if (typeof domInfo === 'string') return document.createTextNode(domInfo);

  const stylesheet: any = {};

  const children = domInfo.children?.map((item, i) => render(item, payload, options, [...indexList, i + 1])) || [];

  const element = createNode(domInfo);

  const className = `class${indexList.join('')}`;

  if (domInfo.styleSheets && Object.keys(domInfo.styleSheets)?.length) {
    element.classList.add(className);
    stylesheet.middle = domInfo.styleSheets;
  }

  // 存在伪元素
  if (domInfo.before?.styleSheets && Object.keys(domInfo.before.styleSheets).length ||
      domInfo.after?.styleSheets && Object.keys(domInfo.after.styleSheets).length) {
    if (domInfo.before?.styleSheets && Object.keys(domInfo.before.styleSheets).length) {
      stylesheet.before = domInfo.before.styleSheets;
    }

    if (domInfo.after?.styleSheets && Object.keys(domInfo.after.styleSheets).length) {
      stylesheet.after = domInfo.after.styleSheets;
    }
  }

  payload.stylesheet[indexList.join('')] = stylesheet;

  if (domInfo.tagName === 'use' && domInfo.attributes?.['xlink:href']) {
    // 找到symbol
    const target = getSvgSymbol(domInfo.attributes['xlink:href']);
    if (target) {
      if (!payload.svg) {
        payload.svg = document.createElement(target.parent.tagName);
        Object.entries(target.parent.attributes).forEach(([key, value]) => {
          payload.svg!.setAttribute(key, value);
        });
      }

      const symbol = createNode({
        tagName: 'symbol',
        attributes: {
          id: target.id,
          viewBox: target.viewBox || ''
        },
        innerHTML: target.innerHTML,
        computedStyles: {},
        styleSheets: {},
        after: { styleSheets: {}, computedStyles: {} },
        before: { styleSheets: {}, computedStyles: {} }
      });

      payload.svg.appendChild(symbol);
    }
  }

  children.forEach(item => element.appendChild(item));

  if (options?.svgUseReplace) {
    if (domInfo.tagName === 'svg') {
      const found = domInfo.children?.find(item => typeof item !== 'string' && item.tagName === 'use') as DomTreeNode;

      if (found && found.attributes?.['xlink:href']) {
        const symbol = getSvgSymbol(found.attributes['xlink:href']);
        if (symbol) {
          element.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
          element.setAttribute('id', symbol.id);
          element.setAttribute('viewBox', symbol.viewBox || '');
          element.innerHTML = symbol.innerHTML || '';
        }
      }
    }
  }

  return element;
}

export function generateCode(domInfo: DomTreeNode, options?: RenderOptions): GeneratedCode {
  let payload: RenderPayload = { svg: null, stylesheet: {}, keyframes: null };
  const element = render(domInfo, payload, options);

  let css = payload.keyframes?.join('\n\n') || '';

  const stylesheets = Object.entries(payload.stylesheet);

  stylesheets.forEach(([index, { before, middle, after }]) => {
    css = [
      css,
      styleMapToCssText(middle, `.class${index}`),
      styleMapToCssText(before, `.class${index}::before`),
      styleMapToCssText(after, `.class${index}::after`)
    ].filter(Boolean).join('\n\n');
  });

  if (options?.svgUseReplace) {
    return {
      css,
      html: (element as Element).outerHTML
    };
  } else {
    return {
      css,
      html: payload.svg ? [payload.svg.outerHTML, (element as Element).outerHTML].join('\n') : (element as Element).outerHTML
    };
  }
}

function styleMapToCssText(map: StyleMap | undefined, selector: string): string {
  if (!map) return '';

  const cssText = Object.entries(map)?.map(([key, value]) => `  ${key}: ${value};`).join('\n');
  return `${selector} {\n${cssText}\n}`;
}
