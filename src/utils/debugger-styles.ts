/**
 * Chrome 调试器 API 样式获取工具
 * 用于通过开发者工具协议获取元素的精确样式信息
 */

import { sendToBackground } from "@plasmohq/messaging";

interface DebuggerTarget {
  tabId: number;
}

interface StyleRule {
  selectorList: {
    selectors: Array<{
      text: string;
    }>;
  };
  style: {
    cssProperties: Array<{
      name: string;
      value: string;
      important?: boolean;
      implicit?: boolean;
      text?: string;
      parsedOk?: boolean;
      disabled?: boolean;
      range?: {
        startLine: number;
        startColumn: number;
        endLine: number;
        endColumn: number;
      };
    }>;
    shorthandEntries: Array<{
      name: string;
      value: string;
      important?: boolean;
    }>;
  };
  origin: 'injected' | 'user-agent' | 'inspector' | 'regular';
  media?: Array<{
    text: string;
    source: string;
  }>;
}

interface MatchedStyles {
  matchedCSSRules: StyleRule[];
  pseudoElements?: Array<{
    pseudoType: string;
    matches: StyleRule[];
  }>;
  inherited?: Array<{
    inlineStyle?: {
      cssProperties: Array<{
        name: string;
        value: string;
        important?: boolean;
      }>;
    };
    matchedCSSRules: StyleRule[];
  }>;
  cssKeyframesRules?: Array<{
    animationName: string;
    keyframes: Array<{
      keyText: string;
      style: {
        cssProperties: Array<{
          name: string;
          value: string;
        }>;
      };
    }>;
  }>;
}

interface ComputedStyles {
  computedStyle: Array<{
    name: string;
    value: string;
  }>;
}

/**
 * Chrome 调试器样式获取器
 */
export class DebuggerStyleExtractor {
  private target: DebuggerTarget | null = null;
  private isAttached = false;

  /**
   * 附加到指定标签页
   */
  async attach(tabId: number): Promise<boolean> {
    try {
      this.target = { tabId };

      // 通过后台脚本附加调试器
      const response = await sendToBackground({
        name: "attachDebugger"
      });

      if (response?.success) {
        this.isAttached = true;
        return true;
      } else {
        console.error('附加调试器失败:', response?.error);
        return false;
      }
    } catch (error) {
      console.error('附加调试器失败:', error);
      return false;
    }
  }

  /**
   * 分离调试器
   */
  async detach(): Promise<void> {
    if (this.target && this.isAttached) {
      try {
        await sendToBackground({
          name: "detachDebugger",
          body: { tabId: this.target.tabId }
        });
      } catch (error) {
        console.error('Failed to detach debugger:', error);
      }
      this.isAttached = false;
      this.target = null;
    }
  }

  /**
   * 发送调试器命令
   */
  private async sendCommand(method: string, params?: any): Promise<any> {
    if (!this.target || !this.isAttached) {
      throw new Error('Debugger not attached');
    }

    try {
      const response = await sendToBackground({
        name: "sendDebuggerCommand",
        body: {
          tabId: this.target.tabId,
          method,
          params
        }
      });

      if (response?.success) {
        return response.result;
      } else {
        throw new Error(response?.error || 'Unknown error');
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * 通过选择器获取节点ID
   */
  private async getNodeIdBySelector(selector: string): Promise<number | null> {
    try {
      const result = await this.sendCommand('DOM.querySelector', {
        nodeId: 1, // document node
        selector
      });
      return result.nodeId || null;
    } catch (error) {
      console.error('Failed to get node by selector:', error);
      return null;
    }
  }

  /**
   * 通过元素位置获取节点ID
   */
  private async getNodeIdByPosition(x: number, y: number): Promise<number | null> {
    try {
      const result = await this.sendCommand('DOM.getNodeForLocation', {
        x,
        y,
        includeUserAgentShadowDOM: true
      });
      return result.nodeId || null;
    } catch (error) {
      console.error('Failed to get node by position:', error);
      return null;
    }
  }

  /**
   * 获取元素的匹配样式
   */
  async getMatchedStyles(nodeId: number): Promise<MatchedStyles | null> {
    try {
      const result = await this.sendCommand('CSS.getMatchedStylesForNode', {
        nodeId
      });
      return result as MatchedStyles;
    } catch (error) {
      console.error('Failed to get matched styles:', error);
      return null;
    }
  }

  /**
   * 获取元素的计算样式
   */
  async getComputedStyles(nodeId: number): Promise<ComputedStyles | null> {
    try {
      const result = await this.sendCommand('CSS.getComputedStyleForNode', {
        nodeId
      });
      return result as ComputedStyles;
    } catch (error) {
      console.error('Failed to get computed styles:', error);
      return null;
    }
  }

  /**
   * 通过元素获取样式信息（主要入口方法）
   */
  async getElementStyles(element: Element): Promise<{
    matchedStyles: MatchedStyles | null;
    computedStyles: ComputedStyles | null;
    nodeId: number | null;
  }> {
    if (!this.isAttached) {
      throw new Error('Debugger not attached');
    }

    let nodeId: number | null = null;

    // 尝试通过多种方式获取节点ID
    // 1. 通过ID选择器
    if (element.id) {
      nodeId = await this.getNodeIdBySelector(`#${element.id}`);
    }

    // 2. 通过类名选择器（如果有唯一类名）
    if (!nodeId && element.className) {
      const classes = element.className.split(' ').filter(c => c.trim());
      for (const cls of classes) {
        const selector = `.${cls}`;
        const tempNodeId = await this.getNodeIdBySelector(selector);
        if (tempNodeId) {
          // TODO: 验证是否是同一个元素
          nodeId = tempNodeId;
          break;
        }
      }
    }

    // 3. 通过位置获取（如果元素在视口中）
    if (!nodeId) {
      const rect = element.getBoundingClientRect();
      if (rect.width > 0 && rect.height > 0) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        nodeId = await this.getNodeIdByPosition(centerX, centerY);
      }
    }

    if (!nodeId) {
      console.warn('Could not find node ID for element');
      return {
        matchedStyles: null,
        computedStyles: null,
        nodeId: null
      };
    }

    // 获取样式信息
    const [matchedStyles, computedStyles] = await Promise.all([
      this.getMatchedStyles(nodeId),
      this.getComputedStyles(nodeId)
    ]);

    return {
      matchedStyles,
      computedStyles,
      nodeId
    };
  }

  /**
   * 将 DevTools 样式格式转换为我们的格式
   */
  static convertMatchedStylesToOurFormat(matchedStyles: MatchedStyles): {
    rules: Array<{
      selectorText: string;
      cssText: string;
      styles: Record<string, string>;
      importants: Record<string, string>;
      origin: string;
      media?: string;
    }>;
    inherited: Array<{
      rules: Array<{
        selectorText: string;
        cssText: string;
        styles: Record<string, string>;
        importants: Record<string, string>;
      }>;
    }>;
    pseudoElements: Record<string, Array<{
      selectorText: string;
      cssText: string;
      styles: Record<string, string>;
      importants: Record<string, string>;
    }>>;
  } {
    const rules: any[] = [];
    const inherited: any[] = [];
    const pseudoElements: Record<string, any[]> = {};

    // 处理匹配的规则
    if (matchedStyles.matchedCSSRules) {
      for (const rule of matchedStyles.matchedCSSRules) {
        const selectorText = rule.selectorList.selectors.map(s => s.text).join(', ');
        const styles: Record<string, string> = {};
        const importants: Record<string, string> = {};
        
        for (const prop of rule.style.cssProperties) {
          if (!prop.disabled && prop.parsedOk !== false) {
            if (prop.important) {
              importants[prop.name] = prop.value;
            } else {
              styles[prop.name] = prop.value;
            }
          }
        }

        // TODO: 生成完整的 cssText
        const cssText = `${selectorText} { /* TODO: generate full cssText */ }`;
        
        rules.push({
          selectorText,
          cssText,
          styles,
          importants,
          origin: rule.origin,
          media: rule.media?.map(m => m.text).join(', ')
        });
      }
    }

    // 处理继承的样式
    if (matchedStyles.inherited) {
      for (const inheritedItem of matchedStyles.inherited) {
        const inheritedRules: any[] = [];
        
        if (inheritedItem.matchedCSSRules) {
          for (const rule of inheritedItem.matchedCSSRules) {
            const selectorText = rule.selectorList.selectors.map(s => s.text).join(', ');
            const styles: Record<string, string> = {};
            const importants: Record<string, string> = {};
            
            for (const prop of rule.style.cssProperties) {
              if (!prop.disabled && prop.parsedOk !== false) {
                if (prop.important) {
                  importants[prop.name] = prop.value;
                } else {
                  styles[prop.name] = prop.value;
                }
              }
            }

            const cssText = `${selectorText} { /* TODO: generate full cssText */ }`;
            
            inheritedRules.push({
              selectorText,
              cssText,
              styles,
              importants
            });
          }
        }
        
        inherited.push({ rules: inheritedRules });
      }
    }

    // 处理伪元素
    if (matchedStyles.pseudoElements) {
      for (const pseudoElement of matchedStyles.pseudoElements) {
        const pseudoType = pseudoElement.pseudoType;
        const pseudoRules: any[] = [];
        
        for (const rule of pseudoElement.matches) {
          const selectorText = rule.selectorList.selectors.map(s => s.text).join(', ');
          const styles: Record<string, string> = {};
          const importants: Record<string, string> = {};
          
          for (const prop of rule.style.cssProperties) {
            if (!prop.disabled && prop.parsedOk !== false) {
              if (prop.important) {
                importants[prop.name] = prop.value;
              } else {
                styles[prop.name] = prop.value;
              }
            }
          }

          const cssText = `${selectorText} { /* TODO: generate full cssText */ }`;
          
          pseudoRules.push({
            selectorText,
            cssText,
            styles,
            importants
          });
        }
        
        pseudoElements[pseudoType] = pseudoRules;
      }
    }

    return {
      rules,
      inherited,
      pseudoElements
    };
  }
}

/**
 * 全局调试器实例
 */
let globalDebuggerExtractor: DebuggerStyleExtractor | null = null;

/**
 * 获取全局调试器实例
 */
export function getDebuggerExtractor(): DebuggerStyleExtractor {
  if (!globalDebuggerExtractor) {
    globalDebuggerExtractor = new DebuggerStyleExtractor();
  }
  return globalDebuggerExtractor;
}

/**
 * 清理全局调试器实例
 */
export async function cleanupDebuggerExtractor(): Promise<void> {
  if (globalDebuggerExtractor) {
    await globalDebuggerExtractor.detach();
    globalDebuggerExtractor = null;
  }
}
