import html2canvas from 'html2canvas';

export interface ThumbnailOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  scale?: number;
  quality?: number;
}

const DEFAULT_OPTIONS: Required<ThumbnailOptions> = {
  width: 320,
  height: 240,
  backgroundColor: '#ffffff',
  scale: 1.0,
  quality: 0.95
};

/**
 * 使用 html2canvas 生成 DOM 元素的缩略图
 */
export const generateDOMThumbnail = async (
  element: Element,
  options: ThumbnailOptions = {}
): Promise<string> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  try {
    // 获取元素尺寸和位置
    const rect = element.getBoundingClientRect();
    const elementWidth = rect.width;
    const elementHeight = rect.height;

    if (elementWidth === 0 || elementHeight === 0) {
      return;
    }

    // 使用 html2canvas 捕获元素
    const canvas = await html2canvas(element as HTMLElement, {
      width: elementWidth,
      height: elementHeight,
      scale: 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: null,
      logging: false,
      removeContainer: true,
      foreignObjectRendering: false, // 禁用以获得更好的兼容性
      imageTimeout: 3000,
      windowWidth: elementWidth,
      windowHeight: elementHeight,
      scrollX: 0,
      scrollY: 0,
      onclone: (clonedDoc, clonedElement) => {
        // 确保克隆的元素可见且样式正确
        const clonedStyle = clonedElement.style;
        clonedStyle.position = 'static';
        clonedStyle.transform = 'none';
        clonedStyle.opacity = '1';
        clonedStyle.visibility = 'visible';
        clonedStyle.display = clonedStyle.display === 'none' ? 'block' : clonedStyle.display;
        clonedStyle.pointerEvents = 'none';

        // 从克隆中移除任何扩展相关的元素
        const extensionElements = clonedDoc.querySelectorAll('[id*="css-cliper"], [class*="css-cliper"]');
        extensionElements.forEach(el => el.remove());
      }
    });

    // 创建具有所需尺寸的最终缩略图画布
    const thumbnailCanvas = document.createElement('canvas');
    const ctx = thumbnailCanvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法获取画布上下文');
    }

    thumbnailCanvas.width = opts.width;
    thumbnailCanvas.height = opts.height;

    // 填充背景
    ctx.fillStyle = opts.backgroundColor;
    ctx.fillRect(0, 0, opts.width, opts.height);

    // 计算缩放比例以适应缩略图中的元素
    const scaleX = opts.width / elementWidth;
    const scaleY = opts.height / elementHeight;
    const scale = Math.min(scaleX, scaleY) * opts.scale;

    // 将元素居中放置在画布中
    const scaledWidth = elementWidth * scale;
    const scaledHeight = elementHeight * scale;
    const offsetX = (opts.width - scaledWidth) / 2;
    const offsetY = (opts.height - scaledHeight) / 2;

    // 将 html2canvas 的结果绘制到缩略图画布上
    ctx.drawImage(canvas, 0, 0, elementWidth, elementHeight, offsetX, offsetY, scaledWidth, scaledHeight);

    return thumbnailCanvas.toDataURL('image/png', opts.quality);

  } catch (error) {
    console.warn('使用 html2canvas 生成 DOM 缩略图时出错:', error);
  }
};

export const isElementVisible = (element: Element): boolean => {
  const rect = element.getBoundingClientRect();
  const style = window.getComputedStyle(element);

  return (
    rect.width > 0 &&
    rect.height > 0 &&
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0'
  );
};
