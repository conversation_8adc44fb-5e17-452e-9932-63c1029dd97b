// 全局标志，用于跟踪扩展上下文状态
let extensionContextInvalidated = false;

// 检查扩展上下文是否有效
export const isExtensionContextValid = (): boolean => {
  if (extensionContextInvalidated) {
    return false;
  }

  try {
    // 多重检查确保上下文有效
    if (!chrome || !chrome.runtime) {
      extensionContextInvalidated = true;
      return false;
    }

    // 尝试访问runtime.id，这是最可靠的检查方式
    const runtimeId = chrome.runtime.id;
    if (!runtimeId) {
      extensionContextInvalidated = true;
      return false;
    }

    return true;
  } catch (error) {
    extensionContextInvalidated = true;
    console.warn('扩展上下文检查失败:', error);
    return false;
  }
};

// 处理上下文失效的函数
const handleContextInvalidation = (): void => {
  extensionContextInvalidated = true;

  // 清理所有可能的监听器和定时器
  try {
    // 移除所有事件监听器
    if (typeof window !== 'undefined') {
      // 清理可能的DOM事件监听器
      document.removeEventListener('keydown', () => {});
      document.body?.removeEventListener('mouseover', () => {});
      document.body?.removeEventListener('click', () => {});
    }
  } catch (error) {
    console.warn('清理过程中出错:', error);
  }

  console.warn('扩展上下文已失效。扩展可能需要重新加载。');
};

// 检查是否需要重新加载页面
export const checkAndHandleContextInvalidation = (): boolean => {
  if (!isExtensionContextValid()) {
    handleContextInvalidation();
    return true;
  }
  return false;
};
