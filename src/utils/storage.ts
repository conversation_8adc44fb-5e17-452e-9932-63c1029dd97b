import type { CodeSnippet, StorageData } from '@/types/snippet';
import { isExtensionContextValid } from './extension-utils';
import { getParameters } from "codesandbox/lib/api/define";

const STORAGE_KEY = 'css_cliper_snippets';
const MAX_SNIPPETS = 1000; // 防止存储溢出

// 为代码片段生成唯一ID
const generateId = (): string => {
  return `snippet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 获取所有存储的代码片段
export const getStoredSnippets = async (): Promise<CodeSnippet[]> => {
  if (!isExtensionContextValid()) {
    throw new Error('扩展上下文无效');
  }

  try {
    const result = await chrome.storage.local.get(STORAGE_KEY);
    const data: StorageData = result[STORAGE_KEY];

    if (!data || !Array.isArray(data.snippets)) {
      return [];
    }

    // 按创建日期排序（最新的在前）
    return data.snippets.sort((a, b) => b.createdAt - a.createdAt);
  } catch (error) {
    console.error('获取存储的代码片段时出错:', error);
    throw new Error('从存储中检索代码片段失败');
  }
};

// 保存新的代码片段
export const saveSnippet = async (
  snippetData: Omit<CodeSnippet, 'id' | 'createdAt' | 'updatedAt'>
): Promise<CodeSnippet> => {
  if (!isExtensionContextValid()) {
    throw new Error('Extension context is invalid');
  }

  try {
    const existingSnippets = await getStoredSnippets();

    // 检查是否达到限制
    if (existingSnippets.length >= MAX_SNIPPETS) {
      throw new Error(`已达到代码片段的最大数量 (${MAX_SNIPPETS})`);
    }

    const now = Date.now();
    const newSnippet: CodeSnippet = {
      ...snippetData,
      id: generateId(),
      createdAt: now,
      updatedAt: now,
    };

    const updatedSnippets = [newSnippet, ...existingSnippets];
    const storageData: StorageData = {
      snippets: updatedSnippets,
      lastUpdated: now,
    };

    await chrome.storage.local.set({ [STORAGE_KEY]: storageData });
    return newSnippet;
  } catch (error) {
    console.error('保存代码片段时出错:', error);
    throw new Error('保存代码片段到存储失败');
  }
};

// 更新现有的代码片段
export const updateSnippet = async (
  id: string,
  updates: Partial<CodeSnippet>
): Promise<CodeSnippet> => {
  if (!isExtensionContextValid()) {
    throw new Error('Extension context is invalid');
  }

  try {
    const existingSnippets = await getStoredSnippets();
    const snippetIndex = existingSnippets.findIndex(s => s.id === id);

    if (snippetIndex === -1) {
      throw new Error('代码片段未找到');
    }

    const updatedSnippet: CodeSnippet = {
      ...existingSnippets[snippetIndex],
      ...updates,
      id, // 确保ID不能被更改
      updatedAt: Date.now(),
    };

    existingSnippets[snippetIndex] = updatedSnippet;

    const storageData: StorageData = {
      snippets: existingSnippets,
      lastUpdated: Date.now(),
    };

    await chrome.storage.local.set({ [STORAGE_KEY]: storageData });
    return updatedSnippet;
  } catch (error) {
    console.error('更新代码片段时出错:', error);
    throw new Error('更新存储中的代码片段失败');
  }
};

// 删除代码片段
export const deleteSnippet = async (id: string): Promise<void> => {
  if (!isExtensionContextValid()) {
    throw new Error('Extension context is invalid');
  }

  try {
    const existingSnippets = await getStoredSnippets();
    const filteredSnippets = existingSnippets.filter(s => s.id !== id);

    if (filteredSnippets.length === existingSnippets.length) {
      throw new Error('Snippet not found');
    }

    const storageData: StorageData = {
      snippets: filteredSnippets,
      lastUpdated: Date.now(),
    };

    await chrome.storage.local.set({ [STORAGE_KEY]: storageData });
  } catch (error) {
    console.error('Error deleting snippet:', error);
    throw new Error('Failed to delete snippet from storage');
  }
};

// Clear all snippets
export const clearAllSnippets = async (): Promise<void> => {
  if (!isExtensionContextValid()) {
    throw new Error('Extension context is invalid');
  }

  try {
    const storageData: StorageData = {
      snippets: [],
      lastUpdated: Date.now(),
    };

    await chrome.storage.local.set({ [STORAGE_KEY]: storageData });
  } catch (error) {
    console.error('Error clearing snippets:', error);
    throw new Error('Failed to clear snippets from storage');
  }
};

// Get storage usage info
export const getStorageInfo = async (): Promise<{
  count: number;
  bytesUsed: number;
  maxBytes: number;
}> => {
  if (!isExtensionContextValid()) {
    throw new Error('Extension context is invalid');
  }

  try {
    const snippets = await getStoredSnippets();
    const bytesUsed = await chrome.storage.local.getBytesInUse(STORAGE_KEY);
    const maxBytes = chrome.storage.local.QUOTA_BYTES;

    return {
      count: snippets.length,
      bytesUsed,
      maxBytes,
    };
  } catch (error) {
    console.error('Error getting storage info:', error);
    throw new Error('Failed to get storage information');
  }
};

// Export snippet as complete HTML file
export const exportSnippetAsHTML = (snippet: CodeSnippet): string => {
  const timestamp = new Date(snippet.createdAt).toISOString().split('T')[0];
  const filename = `${snippet.title.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}`;

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${snippet.title}</title>
    <style>
        /* CSS Cliper - Extracted from: ${snippet.url} */
        /* Created: ${new Date(snippet.createdAt).toLocaleString()} */
        
        ${snippet.css}
    </style>
</head>
<body>
    <!-- HTML extracted from: ${snippet.url} -->
    <!-- Selector: ${snippet.selector || 'N/A'} -->
    
    ${snippet.html}
</body>
</html>`;
};

// Download file helper
export const downloadFile = (content: string, filename: string, mimeType: string = 'text/html'): void => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.click();
  URL.revokeObjectURL(url);
};

// Handle CodePen export
export const exportToCodePen = (snippet: CodeSnippet) => {
  try {
    const form = document.createElement('form');
    form.action = 'https://codepen.io/pen/define';
    form.method = 'POST';
    form.target = '_blank';

    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'data';
    input.value = JSON.stringify({
      title: snippet.title,
      html: snippet.html,
      css: snippet.css,
      description: `Extracted from: ${snippet.url}\nSelector: ${snippet.selector || 'N/A'}`
    });

    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  } catch (error) {
    console.error('Error exporting to CodePen:', error);
    alert('Failed to export to CodePen. Please try again.');
  }
};

// Export snippet to CodeSandbox
export const exportToCodeSandbox = (snippet: CodeSnippet) => {
  try {
    const form = document.createElement('form');
    form.action = 'https://codesandbox.io/api/v1/sandboxes/define';
    form.method = 'POST';
    form.target = '_blank';

    // Create the files structure for CodeSandbox
    const files = {
      'index.html': {
        content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${snippet.title}</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Extracted from: ${snippet.url} -->
    <!-- Selector: ${snippet.selector || 'N/A'} -->

    ${snippet.html}
</body>
</html>`
      },
      'style.css': {
        content: `/* CSS Cliper - Extracted from: ${snippet.url} */
/* Created: ${new Date(snippet.createdAt).toLocaleString()} */

${snippet.css}`
      },
      'package.json': {
        content: {
          name: snippet.title.toLowerCase().replace(/[^a-z0-9]/g, '-'),
          version: '1.0.0',
          description: `Extracted from: ${snippet.url}`,
          main: 'index.html',
          scripts: {
            start: 'parcel index.html --open',
            build: 'parcel build index.html'
          },
          dependencies: {},
          devDependencies: {
            parcel: '^2.0.0'
          }
        }
      }
    };

    // @ts-ignore
    const parameters = getParameters({ files });

    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'parameters';
    input.value = parameters;

    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  } catch (error) {
    console.error('Error exporting to CodeSandbox:', error);
    alert('Failed to export to CodePen. Please try again.');
  }
};
