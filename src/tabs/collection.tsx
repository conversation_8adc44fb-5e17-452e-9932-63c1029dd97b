import React, { useState, useEffect, useCallback, useRef } from 'react';
import SnippetCard from '@/components/SnippetCard';
import ConfirmDialog from '@/components/ConfirmDialog';
import type { CodeSnippet } from '@/types/snippet';
import { getStoredSnippets, deleteSnippet, updateSnippet } from '@/utils/storage';
import '@/styles/collection.scss';
import Search from "react:@/assets/search.svg";
import Close from "react:@/assets/close.svg";

const ITEMS_PER_PAGE = 12;

const Collection = () => {
  const [snippets, setSnippets] = useState<CodeSnippet[]>([]);
  const [filteredSnippets, setFilteredSnippets] = useState<CodeSnippet[]>([]);
  const [displayedSnippets, setDisplayedSnippets] = useState<CodeSnippet[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<{
    show: boolean;
    snippet: CodeSnippet | null;
  }>({ show: false, snippet: null });

  const currentPage = useRef(0);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Load initial data
  useEffect(() => {
    loadSnippets();
  }, []);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (observerRef.current) observerRef.current.disconnect();

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore) {
          loadMoreSnippets();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) observerRef.current.disconnect();
    };
  }, [hasMore, loadingMore]);

  const loadSnippets = async () => {
    try {
      setLoading(true);
      setError(null);
      const allSnippets = await getStoredSnippets();
      setSnippets(allSnippets);
      setFilteredSnippets(allSnippets);

      // Load first page
      const firstPage = allSnippets.slice(0, ITEMS_PER_PAGE);
      setDisplayedSnippets(firstPage);
      setHasMore(allSnippets.length > ITEMS_PER_PAGE);
      currentPage.current = 1;
    } catch (err) {
      setError('Failed to load snippets. Please try again.');
      console.error('Error loading snippets:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadMoreSnippets = useCallback(async () => {
    if (loadingMore || !hasMore) return;

    try {
      setLoadingMore(true);
      const startIndex = currentPage.current * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const nextPage = filteredSnippets.slice(startIndex, endIndex);

      if (nextPage.length > 0) {
        setDisplayedSnippets(prev => [...prev, ...nextPage]);
        currentPage.current += 1;
        setHasMore(endIndex < filteredSnippets.length);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error loading more snippets:', err);
    } finally {
      setLoadingMore(false);
    }
  }, [filteredSnippets, loadingMore, hasMore]);

  // Search functionality
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredSnippets(snippets);
    } else {
      const filtered = snippets.filter(snippet =>
        snippet.title.toLowerCase().includes(query.toLowerCase()) ||
        (snippet.description && snippet.description.toLowerCase().includes(query.toLowerCase()))
      );
      setFilteredSnippets(filtered);
    }

    // Reset pagination
    currentPage.current = 0;
    setDisplayedSnippets([]);
    setHasMore(true);

    // Load first page of filtered results
    setTimeout(() => {
      const firstPage = (query.trim() ?
        snippets.filter(snippet =>
          snippet.title.toLowerCase().includes(query.toLowerCase()) ||
          (snippet.description && snippet.description.toLowerCase().includes(query.toLowerCase()))
        ) : snippets
      ).slice(0, ITEMS_PER_PAGE);

      setDisplayedSnippets(firstPage);
      setHasMore(firstPage.length === ITEMS_PER_PAGE &&
        (query.trim() ?
          snippets.filter(snippet =>
            snippet.title.toLowerCase().includes(query.toLowerCase()) ||
            (snippet.description && snippet.description.toLowerCase().includes(query.toLowerCase()))
          ).length > ITEMS_PER_PAGE : snippets.length > ITEMS_PER_PAGE
        )
      );
      currentPage.current = 1;
    }, 0);
  };

  const handleDeleteClick = (snippet: CodeSnippet) => {
    setDeleteConfirm({ show: true, snippet });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirm.snippet) return;

    try {
      await deleteSnippet(deleteConfirm.snippet.id);

      // Update local state
      const updatedSnippets = snippets.filter(s => s.id !== deleteConfirm.snippet!.id);
      setSnippets(updatedSnippets);
      setDisplayedSnippets(prev => prev.filter(s => s.id !== deleteConfirm.snippet!.id));

      setDeleteConfirm({ show: false, snippet: null });
    } catch (err) {
      console.error('Error deleting snippet:', err);
      setError('Failed to delete snippet. Please try again.');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirm({ show: false, snippet: null });
  };

  const handleUpdateSnippet = async (id: string, updates: Partial<CodeSnippet>) => {
    try {
      await updateSnippet(id, updates);

      // Update local state
      const updatedSnippets = snippets.map(s =>
        s.id === id ? { ...s, ...updates, updatedAt: Date.now() } : s
      );
      setSnippets(updatedSnippets);
      setDisplayedSnippets(prev => prev.map(s =>
        s.id === id ? { ...s, ...updates, updatedAt: Date.now() } : s
      ));
    } catch (err) {
      console.error('Error updating snippet:', err);
      setError('Failed to update snippet. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="collection-container">
        <div className="collection-header">
          <h1>Code Snippets Collection</h1>
        </div>
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading your code snippets...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="collection-container">
        <div className="collection-header">
          <h1>Code Snippets Collection</h1>
        </div>
        <div className="error-state">
          <p>{error}</p>
          <button onClick={loadSnippets} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="collection-container">
      <div className="collection-header">
        <h1>Code Snippets Collection</h1>
        <p className="collection-stats">
          {snippets.length} snippet{snippets.length !== 1 ? 's' : ''} collected
        </p>

        {/* Search Box */}
        <div className="search-container">
          <div className="search-input-wrapper">
            <Search className="search-icon" />
            <input
              type="text"
              placeholder="Search by title or description..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                onClick={() => handleSearchChange('')}
                className="search-clear"
                title="Clear search"
              >
                <Close />
              </button>
            )}
          </div>
          {searchQuery && (
            <p className="search-results">
              {filteredSnippets.length} result{filteredSnippets.length !== 1 ? 's' : ''} found
            </p>
          )}
        </div>
      </div>

      {displayedSnippets.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">📝</div>
          <h2>No Code Snippets Yet</h2>
          <p>Start collecting DOM + CSS snippets from web pages using the extension.</p>
        </div>
      ) : (
        <>
          <div className="snippets-grid">
            {displayedSnippets.map((snippet) => (
              <SnippetCard
                key={snippet.id}
                snippet={snippet}
                onDelete={handleDeleteClick}
                onUpdate={handleUpdateSnippet}
              />
            ))}
          </div>

          {hasMore && (
            <div ref={loadMoreRef} className="load-more-trigger">
              {loadingMore && (
                <div className="loading-more">
                  <div className="loading-spinner small"></div>
                  <span>Loading more snippets...</span>
                </div>
              )}
            </div>
          )}
        </>
      )}

      <ConfirmDialog
        show={deleteConfirm.show}
        title="Delete Code Snippet"
        message={`Are you sure you want to delete "${deleteConfirm.snippet?.title || 'this snippet'}"? This action cannot be undone.`}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </div>
  );
};

export default Collection;
