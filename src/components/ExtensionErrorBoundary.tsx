import React from 'react';
import { checkAndHandleContextInvalidation } from '@/utils/extension-utils.ts';

class ExtensionErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('扩展错误边界捕获到错误:', error, errorInfo);

    if (error.message?.includes('Extension context invalidated')) {
      checkAndHandleContextInvalidation();
    }
  }

  render() {
    if (this.state.hasError) {
      // 如果是扩展上下文失效，不渲染任何内容
      if (this.state.error?.message?.includes('Extension context invalidated')) {
        return null;
      }

      // 其他错误显示简单的错误信息
      return (
        <div style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: 'red',
          color: 'white',
          padding: '10px',
          borderRadius: '4px',
          zIndex: 999999
        }}>
          Extension Error: {this.state.error?.message || 'Unknown error'}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ExtensionErrorBoundary;
