import React, { useEffect } from 'react';
import type { ConfirmDialogProps } from '@/types/snippet';
import Close from "react:@/assets/close.svg";
import Warning from "react:@/assets/warning.svg";

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  show,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = 'Delete',
  cancelText = 'Cancel'
}) => {
  // 处理ESC键
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && show) {
        onCancel();
      }
    };

    if (show) {
      document.addEventListener('keydown', handleEscape);
      // 当对话框打开时防止页面滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [show, onCancel]);

  // 处理背景点击
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onCancel();
    }
  };

  if (!show) return null;

  return (
    <div className="confirm-dialog-overlay" onClick={handleBackdropClick}>
      <div className="confirm-dialog">
        <div className="confirm-dialog-header">
          <h3 className="confirm-dialog-title">{title}</h3>
          <button
            className="confirm-dialog-close"
            onClick={onCancel}
            aria-label="Close dialog"
          >
            <Close style={{ width: 20, height: 20 }} />
          </button>
        </div>

        <div className="confirm-dialog-content">
          <div className="confirm-dialog-icon">
            <Warning />
          </div>
          <p className="confirm-dialog-message">{message}</p>
        </div>

        <div className="confirm-dialog-actions">
          <button
            className="confirm-dialog-button cancel-button"
            onClick={onCancel}
          >
            {cancelText}
          </button>
          <button
            className="confirm-dialog-button confirm-button"
            onClick={onConfirm}
            autoFocus
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDialog;
