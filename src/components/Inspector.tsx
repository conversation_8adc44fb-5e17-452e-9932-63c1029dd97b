import React, { useState, useEffect, useMemo } from 'react';
import {
  saveSnippet,
  exportSnippetAsHTML,
  downloadFile,
  exportToCodeSandbox,
  exportToCodePen
} from '@/utils/storage';
import { generateDOMThumbnail, isElementVisible } from '@/utils/dom-thumbnail';
import CodePen from "react:@/assets/codepen.svg";
import CodeSandbox from "react:@/assets/codesandbox.svg";
import DragHandle from "react:@/assets/drag-handle.svg";
import Close from "react:@/assets/close.svg";
import ArrowLeft from "react:@/assets/arrow-left.svg";
import ArrowUp from "react:@/assets/arrow-up.svg";
import ArrowDown from "react:@/assets/arrow-down.svg";
import ArrowRight from "react:@/assets/arrow-right.svg";
import Check from "react:@/assets/check.svg";
import Save from "react:@/assets/save.svg";

interface InspectorProps {
  code: { html: string; css: string };
  onSelect: (direction: string) => void;
  onClose?: () => void;
  selector?: string;
  domInfo?: any;
  element?: Element | EventTarget | null;
}

const Inspector = ({ code, onSelect, onClose, selector, domInfo, element }: InspectorProps) => {
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);
  const [thumbnail, setThumbnail] = useState<string>('');
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // 初始化位置到右下角
  const [position, setPosition] = useState(() => {
    const inspectorWidth = 320;
    const inspectorHeight = 462;
    return {
      x: window.innerWidth - inspectorWidth - 20,
      y: window.innerHeight - inspectorHeight - 20
    };
  });

  // 添加CSS动画关键帧
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 当元素变化时生成缩略图（带防抖）
  useEffect(() => {
    const generateThumbnail = async () => {
      if (element && isElementVisible(element as Element)) {
        try {
          const thumbnailData = await generateDOMThumbnail(element as Element, {
            width: 320,
            height: 240,
            scale: 1.0
          });
          setThumbnail(thumbnailData);
        } catch (error) {
          console.warn('生成缩略图失败:', error);
          setThumbnail('');
        }
      } else {
        setThumbnail('');
      }
    };

    // 防抖缩略图生成
    const timeoutId = setTimeout(generateThumbnail, 300);
    return () => clearTimeout(timeoutId);
  }, [element]);

  // 拖拽功能
  const handleMouseDown = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest('.drag-handle')) {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        e.preventDefault();
        e.stopPropagation();
        const newX = e.clientX - dragOffset.x;
        const newY = e.clientY - dragOffset.y;

        // 保持在视口边界内
        const maxX = window.innerWidth - 320; // 检查器宽度
        const maxY = window.innerHeight - 400; // 检查器高度

        setPosition({
          x: Math.max(0, Math.min(newX, maxX)),
          y: Math.max(0, Math.min(newY, maxY))
        });
      }
    };

    const handleMouseUp = (e: MouseEvent) => {
      if (isDragging) {
        e.preventDefault();
        e.stopPropagation();
      }
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove, true);
      document.addEventListener('mouseup', handleMouseUp, true);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove, true);
      document.removeEventListener('mouseup', handleMouseUp, true);
    };
  }, [isDragging, dragOffset]);

  // 从元素中提取标签名
  const tagName = useMemo(() => {
    if (element && 'tagName' in element) {
      return (element as Element).tagName.toLowerCase();
    }
    return 'element';
  }, [element]);

  const handleSave = async () => {
    if (saving || saved) return;

    try {
      setSaving(true);

      // 从标签名生成标题
      const title = `<${tagName}> element`;
      const url = window.location.href;

      const savedSnippet = await saveSnippet({
        title,
        html: code.html,
        css: code.css,
        url,
        selector,
        domInfo,
        description: `Extracted from ${new URL(url).hostname}`
      });

      // 生成并下载HTML文件
      const htmlContent = exportSnippetAsHTML(savedSnippet);
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `${tagName}_element_${timestamp}.html`;
      downloadFile(htmlContent, filename, 'text/html');

      setSaved(true);

      // 3秒后重置保存状态
      setTimeout(() => setSaved(false), 3000);
    } catch (error) {
      console.error('保存代码片段时出错:', error);
      alert('Failed to save snippet. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // 处理CodePen导出
  const handleCodePenExport = () => {
    const snippetData = {
      title: `CSS Picker - <${tagName}>`,
      html: code.html,
      css: code.css,
      url: window.location.href,
      selector,
      domInfo,
      description: `Extracted from ${new URL(window.location.href).hostname}`,
      createdAt: Date.now()
    };

    exportToCodePen(snippetData as any);
  }

  // 处理CodeSandbox导出
  const handleCodeSandboxExport = async () => {
    const snippetData = {
      title: `CSS Picker - <${tagName}>`,
      html: code.html,
      css: code.css,
      url: window.location.href,
      selector,
      domInfo,
      description: `Extracted from ${new URL(window.location.href).hostname}`,
      createdAt: Date.now()
    };

    await exportToCodeSandbox(snippetData as any);
  };

  // 图标按钮样式辅助函数
  const getIconButtonStyle = () => ({
    padding: '10px',
    background: '#f8fafc',
    border: '1px solid #e2e8f0',
    borderRadius: '8px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#64748b',
    transition: 'all 0.2s ease',
    fontSize: '14px',
    fontWeight: '500',
    minWidth: '40px',
    height: '40px'
  } as React.CSSProperties);

  return (
    <div
      style={{
        position: 'fixed',
        top: position.y,
        left: position.x,
        background: 'white',
        padding: 0,
        borderRadius: 12,
        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
        zIndex: 999999,
        width: '320px',
        pointerEvents: 'auto',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        cursor: isDragging ? 'grabbing' : 'default'
      }}
      onMouseDown={handleMouseDown}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onMouseMove={(e) => {
        e.stopPropagation();
      }}
    >
      {/* Drag Handle */}
      <div
        className="drag-handle"
        style={{
          background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
          color: 'white',
          padding: '8px 16px',
          borderRadius: '12px 12px 0 0',
          cursor: isDragging ? 'grabbing' : 'grab',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          fontSize: '12px',
          fontWeight: '600',
          userSelect: 'none'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DragHandle style={{ marginRight: 8 }} />
          CSS Cliper
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            padding: '2px 8px',
            borderRadius: '4px',
            fontSize: '11px',
            fontWeight: '500'
          }}>
            &lt;{tagName}&gt;
          </div>
          {onClose && (
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onClose();
              }}
              style={{
                background: 'rgba(255, 255, 255, 0.2)',
                border: 'none',
                borderRadius: '4px',
                padding: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                transition: 'background-color 0.2s ease',
                width: '20px',
                height: '20px'
              }}
              title="Close Inspector"
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
              }}
            >
              <Close />
            </button>
          )}
        </div>
      </div>

      <div style={{ padding: 16 }}>
        {/* Thumbnail */}
        {thumbnail && (
          <div style={{
            marginBottom: 16,
            borderRadius: 8,
            overflow: 'hidden',
            border: '2px solid #e5e7eb',
            background: '#f8fafc',
            minHeight: '180px'
          }}>
            <img
              src={thumbnail}
              alt="Element preview"
              style={{
                width: '100%',
                height: 'auto',
                display: 'block',
                minHeight: '180px',
                objectFit: 'contain'
              }}
            />
          </div>
        )}

        {/* Navigation buttons with icons - single row */}
        <div style={{
          display: 'flex',
          gap: 8,
          marginBottom: 16,
          justifyContent: 'center'
        }}>
          <button
            onClick={() => onSelect('ArrowLeft')}
            style={getIconButtonStyle()}
            title="Previous sibling"
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#8b5cf6';
              e.currentTarget.style.color = 'white';
              e.currentTarget.style.borderColor = '#8b5cf6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#f8fafc';
              e.currentTarget.style.color = '#64748b';
              e.currentTarget.style.borderColor = '#e2e8f0';
            }}
          >
            <ArrowLeft />
          </button>
          <button
            onClick={() => onSelect('ArrowUp')}
            style={getIconButtonStyle()}
            title="Parent element"
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#8b5cf6';
              e.currentTarget.style.color = 'white';
              e.currentTarget.style.borderColor = '#8b5cf6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#f8fafc';
              e.currentTarget.style.color = '#64748b';
              e.currentTarget.style.borderColor = '#e2e8f0';
            }}
          >
            <ArrowUp />
          </button>
          <button
            onClick={() => onSelect('ArrowDown')}
            style={getIconButtonStyle()}
            title="Child element"
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#8b5cf6';
              e.currentTarget.style.color = 'white';
              e.currentTarget.style.borderColor = '#8b5cf6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#f8fafc';
              e.currentTarget.style.color = '#64748b';
              e.currentTarget.style.borderColor = '#e2e8f0';
            }}
          >
            <ArrowDown />
          </button>
          <button
            onClick={() => onSelect('ArrowRight')}
            style={getIconButtonStyle()}
            title="Next sibling"
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#8b5cf6';
              e.currentTarget.style.color = 'white';
              e.currentTarget.style.borderColor = '#8b5cf6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = '#f8fafc';
              e.currentTarget.style.color = '#64748b';
              e.currentTarget.style.borderColor = '#e2e8f0';
            }}
          >
            <ArrowRight />
          </button>
        </div>

        {/* Action buttons */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: 8,
          borderTop: '1px solid #e5e7eb',
          paddingTop: 16
        }}>
          {/* First row - Save button */}
          <button
            onClick={handleSave}
            disabled={saving || saved}
            style={{
              padding: '12px 16px',
              background: saved ? '#10b981' : (saving ? '#9ca3af' : '#8b5cf6'),
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: saving || saved ? 'default' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '6px',
              transition: 'all 0.2s ease',
              opacity: saving || saved ? 0.8 : 1
            }}
            onMouseEnter={(e) => {
              if (!saving && !saved) {
                e.currentTarget.style.background = '#7c3aed';
              }
            }}
            onMouseLeave={(e) => {
              if (!saving && !saved) {
                e.currentTarget.style.background = '#8b5cf6';
              }
            }}
          >
            {saving ? (
              <>
                <div style={{
                  width: '14px',
                  height: '14px',
                  border: '2px solid transparent',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
                Saving...
              </>
            ) : saved ? (
              <>
                <Check />
                Saved
              </>
            ) : (
              <>
                <Save />
                Save
              </>
            )}
          </button>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: 8
          }}>
            <button
              onClick={handleCodePenExport}
              style={{
                padding: '12px 16px',
                background: '#1e293b',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '6px',
                transition: 'all 0.2s ease'
              }}
              title="Open in CodePen"
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#0f172a';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = '#1e293b';
              }}
            >
              <CodePen/>
              CodePen
            </button>

            <button
              onClick={handleCodeSandboxExport}
              style={{
                padding: '12px 16px',
                background: '#151515',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '6px',
                transition: 'all 0.2s ease'
              }}
              title="Open in CodeSandbox"
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#0a0a0a';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = '#151515';
              }}
            >
              <CodeSandbox/>
              CodeSandbox
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Inspector;
