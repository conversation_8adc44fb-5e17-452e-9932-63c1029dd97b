import dayjs from 'dayjs';
import React, { useState, useRef, useEffect } from 'react';
import type { SnippetCardProps } from '@/types/snippet';
import { exportSnippetAsHTML, downloadFile, exportToCodeSandbox, exportToCodePen } from '@/utils/storage';
import CodePen from "react:@/assets/codepen.svg";
import CodeSandbox from "react:@/assets/codesandbox.svg";
import Download from "react:@/assets/download.svg";
import Trash from "react:@/assets/trash.svg";

const SnippetCard: React.FC<SnippetCardProps> = ({ snippet, onDelete, onUpdate }) => {
  const [previewLoaded, setPreviewLoaded] = useState(false);
  const [previewError, setPreviewError] = useState(false);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editTitle, setEditTitle] = useState(snippet.title);
  const [editDescription, setEditDescription] = useState(snippet.description || '');
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const titleInputRef = useRef<HTMLInputElement>(null);
  const descriptionInputRef = useRef<HTMLTextAreaElement>(null);

  // Create preview content for iframe
  const createPreviewContent = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { 
            margin: 0; 
            padding: 8px; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            overflow: hidden;
          }
          * { 
            box-sizing: border-box; 
          }
          ${snippet.css}
        </style>
      </head>
      <body>
        ${snippet.html}
      </body>
      </html>
    `;
  };

  // Load preview in iframe
  useEffect(() => {
    if (iframeRef.current) {
      try {
        const iframe = iframeRef.current;
        const doc = iframe.contentDocument || iframe.contentWindow?.document;

        if (doc) {
          doc.open();
          doc.write(createPreviewContent());
          doc.close();
          setPreviewLoaded(true);
          setPreviewError(false);
        }
      } catch (error) {
        console.error('Error loading preview:', error);
        setPreviewError(true);
      }
    }
  }, [snippet]);

  // Handle CodePen export
  const handleCodePenExport = () => {
    exportToCodePen(snippet);
  };

  // Handle CodeSandbox export
  const handleCodeSandboxExport = async () => {
    exportToCodeSandbox(snippet)
  };

  // Handle download as HTML
  const handleDownload = () => {
    const htmlContent = exportSnippetAsHTML(snippet);
    const timestamp = new Date(snippet.createdAt).toISOString().split('T')[0];
    const filename = `${snippet.title.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.html`;
    downloadFile(htmlContent, filename);
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm');
  };

  // Truncate URL for display
  const truncateUrl = (url: string, maxLength: number = 40) => {
    if (url.length <= maxLength) return url;
    return url.substring(0, maxLength) + '...';
  };

  // Handle URL click
  const handleUrlClick = () => {
    window.open(snippet.url, '_blank', 'noopener,noreferrer');
  };

  // Handle title edit
  const handleTitleEdit = () => {
    setIsEditingTitle(true);
    setTimeout(() => titleInputRef.current?.focus(), 0);
  };

  const handleTitleSave = () => {
    if (editTitle.trim() && editTitle !== snippet.title) {
      onUpdate(snippet.id, { title: editTitle.trim() });
    }
    setIsEditingTitle(false);
  };

  const handleTitleCancel = () => {
    setEditTitle(snippet.title);
    setIsEditingTitle(false);
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSave();
    } else if (e.key === 'Escape') {
      handleTitleCancel();
    }
  };

  // Handle description edit
  const handleDescriptionEdit = () => {
    setIsEditingDescription(true);
    setTimeout(() => descriptionInputRef.current?.focus(), 0);
  };

  const handleDescriptionSave = () => {
    if (editDescription !== (snippet.description || '')) {
      onUpdate(snippet.id, { description: editDescription });
    }
    setIsEditingDescription(false);
  };

  const handleDescriptionCancel = () => {
    setEditDescription(snippet.description || '');
    setIsEditingDescription(false);
  };

  const handleDescriptionKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleDescriptionSave();
    } else if (e.key === 'Escape') {
      handleDescriptionCancel();
    }
  };

  return (
    <div className="snippet-card">
      <div className="snippet-preview">
        {previewError ? (
          <div className="preview-error">
            <div className="error-icon">⚠️</div>
            <p>Preview unavailable</p>
          </div>
        ) : (
          <iframe
            ref={iframeRef}
            className={`preview-iframe ${previewLoaded ? 'loaded' : ''}`}
            title={`Preview of ${snippet.title}`}
            sandbox="allow-same-origin"
            loading="lazy"
          />
        )}
        {!previewLoaded && !previewError && (
          <div className="preview-loading">
            <div className="loading-spinner small"></div>
          </div>
        )}

        {/* 创建时间显示在预览区右下角 */}
        <div className="preview-timestamp">
          {formatDate(snippet.createdAt)}
        </div>

        {/* 操作按钮放在预览区右下角 */}
        <div className="preview-actions">
          <button
            className="action-button codepen-button"
            onClick={handleCodePenExport}
            title="Open in CodePen"
            aria-label="Open in CodePen"
          >
            <CodePen/>
          </button>

          <button
            className="action-button codesandbox-button"
            onClick={handleCodeSandboxExport}
            title="Open in CodeSandbox"
            aria-label="Open in CodeSandbox"
          >
            <CodeSandbox/>
          </button>

          <button
            className="action-button download-button"
            onClick={handleDownload}
            title="Download as HTML file"
            aria-label="Download as HTML file"
          >
            <Download />
          </button>

          <button
            className="action-button delete-button"
            onClick={() => onDelete(snippet)}
            title="Delete snippet"
            aria-label="Delete snippet"
          >
            <svg viewBox="0 0 24 24" width="14" height="14">
              <path
                fill="currentColor"
                d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"
              />
            </svg>
          </button>
        </div>
      </div>

      <div className="snippet-content">
        <div className="snippet-header">
          <div className="snippet-title-container">
            <div className="title-edit-container">
              <input
                ref={titleInputRef}
                type="text"
                value={isEditingTitle ? editTitle : snippet.title}
                onChange={(e) => {
                  if (isEditingTitle) {
                    setEditTitle(e.target.value);
                  }
                }}
                onKeyDown={handleTitleKeyDown}
                onBlur={isEditingTitle ? handleTitleSave : undefined}
                onClick={!isEditingTitle ? handleTitleEdit : undefined}
                className={`title-input ${isEditingTitle ? 'editing' : 'viewing'}`}
                maxLength={100}
                readOnly={!isEditingTitle}
                title={isEditingTitle ? "Press Enter to save, Escape to cancel" : "Click to edit"}
              />
            </div>
          </div>

        </div>

        <div className="snippet-description-container">
          <div className="description-edit-container">
            <textarea
              ref={descriptionInputRef}
              value={isEditingDescription ? editDescription : (snippet.description || '')}
              onChange={(e) => {
                if (isEditingDescription) {
                  setEditDescription(e.target.value);
                }
              }}
              onKeyDown={handleDescriptionKeyDown}
              onBlur={isEditingDescription ? handleDescriptionSave : undefined}
              onClick={!isEditingDescription ? handleDescriptionEdit : undefined}
              className={`description-input ${isEditingDescription ? 'editing' : 'viewing'} ${!snippet.description && !isEditingDescription ? 'empty' : ''}`}
              placeholder="Add a description..."
              maxLength={500}
              rows={2}
              readOnly={!isEditingDescription}
              title={isEditingDescription ? "Press Ctrl+Enter to save, Escape to cancel" : "Click to edit"}
            />
          </div>
        </div>
      </div>


    </div>
  );
};

export default SnippetCard;
