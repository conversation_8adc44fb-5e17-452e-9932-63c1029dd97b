import { useEffect, useMemo, useState } from 'react';

const SelectedBlock = ({ active, candidate }) => {
  const [value, setValue] = useState(false);

  const activeRect = useMemo(() => active?.getBoundingClientRect(), [active, value]);
  const candidateRect = useMemo(() => candidate?.getBoundingClientRect(), [candidate]);

  useEffect(() => {
    const intervalId = setInterval(() => setValue(v => !v), 200);

    return () => clearInterval(intervalId);
  }, []);

  return (
    <>
      <div style={{
        position: 'absolute',
        transition: 'all 0.1s ease-in-out 0s',
        background: 'rgba(79, 70, 229, 0.3)',
        border: '1px solid deeppink',
        top: activeRect?.top,
        left: activeRect?.left,
        height: activeRect?.height,
        width: activeRect?.width,
      }}/>
      {
        candidate && <div className="mask" style={{
          position: 'absolute',
          inset: 0,
          transition: '0.3s',
          clipPath: `
        polygon(0% 0%, 0% 100%,
        ${candidateRect?.left}px 100%,
        ${candidateRect?.left}px ${candidateRect?.top}px,
        ${candidateRect?.left + candidateRect?.width}px ${candidateRect?.top}px,
        ${candidateRect?.left + candidateRect?.width}px ${candidateRect?.top + candidateRect?.height}px,
        ${candidateRect?.left}px ${candidateRect?.top + candidateRect?.height}px,
        ${candidateRect?.left}px 100%,
        100% 100%,
        100% 0%)`,
          background: 'rgba(0,0,0,0.7)'
        }}/>
      }
    </>
  );
};

export default SelectedBlock;
