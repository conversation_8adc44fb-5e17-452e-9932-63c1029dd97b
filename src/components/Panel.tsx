import React, { useEffect, useMemo, useState } from 'react';
import { buildDomTree, generateCode } from '@/utils/css-picker';
import SelectedBlock from './SelectedBlock';
import Inspector from '@/components/Inspector.tsx';
import { sendToBackground } from "@plasmohq/messaging";

const Panel = () => {
  // 当前选中的元素
  const [element, setElement] = useState<Element | EventTarget | null>(null);
  const [hoverElement, setHoverElement] = useState<Element | EventTarget | null>(null);
  const [code, setCode] = useState({ html: '', css: '' });
  const [domInfo, setDomInfo] = useState<any>(null);
  const [panelVisible, setPanelVisible] = useState(false);

  function waitSelectElement(event) {
    setHoverElement(event.target);
    event.stopPropagation();
    event.preventDefault();
  }

  // 选择元素并发送元素信息到弹出窗口
  async function selectElement(event) {
    try {
      // 构建包含当前元素的DOM树结构并输出属性和标签树
      const result = await buildDomTree(event.target);
      // 生成HTML代码
      const { html, css } = generateCode(result, { svgUseReplace: true });
      setCode({ html, css });
      setDomInfo(result);
      // try {
      //   const msg = await sendToBackground({
      //     name: "showElementInfo",
      //     body: { data: result }
      //   });
      //   console.log('Element info sent to background:', msg);
      // } catch (error) {
      //   console.error('Failed to send element info to background:', error);
      // }
      console.log(result, '---------------');
      setPanelVisible(true);
    } catch (e) {
      console.error(e);
    } finally {
      event.preventDefault();
      event.stopPropagation();
      setElement(event.target);
    }
  }

  async function handleKeydown(event) {
    // 检查按下的是否是ESC键（键码为27）
    if (event.keyCode === 27) {
      try {
        await sendToBackground({
          name: "exitElementSelection"
        });
      } catch (error) {
        console.error('发送退出元素选择消息失败:', error);
      }
      setHoverElement(null);
      document.body.removeEventListener('mouseover', waitSelectElement);
      document.body.removeEventListener('click', selectElement, true);
    }

    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      setElement(element => getTargetElement(element, event.key));
    }
  }

  const getTargetElement = (element, direction) => {
    let el;

    switch (direction) {
      case 'ArrowUp':
        // @ts-ignore
        el = element?.parentElement || element;
        break;
      case 'ArrowDown':
        // @ts-ignore
        el = element?.firstElementChild || element;
        break;
      case 'ArrowLeft':
        // @ts-ignore
        el = element?.previousElementSibling || element;
        break;
      case 'ArrowRight':
        // @ts-ignore
        el = element?.nextElementSibling || element;
        break;
      default:
        el = element;
        break;
    }
    if (['STYLE', 'SCRIPT'].includes(el.tagName)) {
      return getTargetElement(el, direction);
    } else {
      return el;
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeydown);

    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      // 处理接收到的消息
      if (request.action === 'startElementSelection') {
        document.body.addEventListener('mouseover', waitSelectElement);
        document.body.addEventListener('click', selectElement, true);
      }

      if (request.action === 'stopElementSelection') {
        cancelClip();
      }
    });

    return () => {
      document.removeEventListener('keydown', handleKeydown);
      cancelClip();
    };
  }, []);

  const cancelClip = () => {
    setHoverElement(null);
    setElement(null);
    document.body.removeEventListener('mouseover', waitSelectElement);
    document.body.removeEventListener('click', selectElement, true);
  };

  const handleSelect = (direction) => {
    setElement(element => getTargetElement(element, direction));
  };

  const handleCloseInspector = async () => {
    try {
      await sendToBackground({
        name: "exitElementSelection"
      });
    } catch (error) {
      console.error('发送退出元素选择消息失败:', error);
    }
    cancelClip();
    setPanelVisible(false);
  };

  const selector = useMemo(() => {
    if (!element) return;

    // @ts-ignore
    const tagName = element.tagName.toLowerCase();
    // @ts-ignore
    const id = element.id ? `#${element.id}` : '';
    // @ts-ignore
    const classNames = Array.from(element.classList).map(item => `.${item}`).join('');

    return tagName + id + classNames;
  }, [element]);

  // 计算默认检查器位置（右下角作为后备）
  const getDefaultInspectorPosition = () => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const inspectorWidth = 320;
    const inspectorHeight = 400;

    return {
      x: Math.max(20, viewportWidth - inspectorWidth - 20),
      y: Math.max(20, viewportHeight - inspectorHeight - 20)
    };
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      zIndex: 99999,
      pointerEvents: 'none',
      margin: 0
    }}>
      <SelectedBlock active={element} candidate={hoverElement}/>
      {
        panelVisible &&
        <Inspector
          code={code}
          onSelect={handleSelect}
          onClose={handleCloseInspector}
          selector={selector}
          domInfo={domInfo}
          element={element}
        />
      }
    </div>
  );
};

export default Panel;
