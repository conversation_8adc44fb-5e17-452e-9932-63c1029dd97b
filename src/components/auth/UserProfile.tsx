import React from "react"

import { useFirebaseAuth } from "@/contexts/FirebaseAuthContext"

/**
 * 用户资料组件示例
 * 展示如何在任何组件中直接使用 Firebase Auth Context
 */
export default function UserProfile() {
  const { user, onLogout, onSendVerificationEmail } = useFirebaseAuth()

  if (!user) {
    return null // 如果没有用户，不显示组件
  }

  const handleLogout = async () => {
    try {
      await onLogout()
    } catch (error: any) {
      console.error("退出登录失败:", error.message)
      alert("退出登录失败: " + error.message)
    }
  }

  const handleSendVerification = async () => {
    try {
      const sent = await onSendVerificationEmail()
      if (sent) {
        alert("验证邮件已发送！")
      } else {
        alert("邮箱已验证或发送失败")
      }
    } catch (error: any) {
      console.error("发送验证邮件失败:", error.message)
      alert("发送验证邮件失败: " + error.message)
    }
  }

  return (
    <div className="user-profile">
      <h3>用户资料</h3>

      <div className="user-info">
        <p><strong>邮箱:</strong> {user.email}</p>
        <p><strong>用户ID:</strong> {user.uid}</p>
        <p><strong>创建时间:</strong> {user.metadata.creationTime}</p>
        <p><strong>最后登录:</strong> {user.metadata.lastSignInTime}</p>

        <div className="verification-status">
          {user.emailVerified ? (
            <p className="verified">✅ 邮箱已验证</p>
          ) : (
            <div className="not-verified">
              <p className="warning">⚠️ 邮箱未验证</p>
              <button onClick={handleSendVerification}>
                发送验证邮件
              </button>
            </div>
          )}
        </div>

        {user.providerData.length > 0 && (
          <div className="provider-info">
            <p><strong>登录方式:</strong></p>
            <ul>
              {user.providerData.map((provider, index) => (
                <li key={index}>
                  {provider.providerId === 'google.com' && '🔍 Google'}
                  {provider.providerId === 'github.com' && '🐙 GitHub'}
                  {provider.providerId === 'password' && '📧 邮箱密码'}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      <div className="actions">
        <button onClick={handleLogout} className="logout-btn">
          退出登录
        </button>
      </div>
    </div>
  )
}
