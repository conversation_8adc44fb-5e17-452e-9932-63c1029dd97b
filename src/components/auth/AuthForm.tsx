import React, { useState } from "react"

import { useFirebaseAuth } from "@/contexts/FirebaseAuthContext"
import Google from "react:@/assets/google.svg";
import Github from "react:@/assets/github.svg";

export default function AuthForm() {
  const [isLogin, setIsLogin] = useState(true)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const { onLogin, onRegister, onGoogleLogin, onGithubLogin } = useFirebaseAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    // 基本验证
    if (!email || !password) {
      setError("请填写所有必填字段")
      setIsLoading(false)
      return
    }

    if (!isLogin && password !== confirmPassword) {
      setError("两次输入的密码不一致")
      setIsLoading(false)
      return
    }

    if (!isLogin && password.length < 6) {
      setError("密码至少需要6位字符")
      setIsLoading(false)
      return
    }

    try {
      if (isLogin) {
        await onLogin(email, password)
      } else {
        await onRegister(email, password)
        // 注册成功后显示提示
        alert("注册成功！验证邮件已发送到您的邮箱，请查收并点击验证链接。")
      }
      // 成功后清空表单
      setEmail("")
      setPassword("")
      setConfirmPassword("")
    } catch (error: any) {
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleLogin = async () => {
    if (isLoading) return // 防止重复点击

    setError("")
    setIsLoading(true)

    try {
      console.log("用户点击 Google 登录按钮")
      await onGoogleLogin()
      // Google 登录成功，清空表单
      setEmail("")
      setPassword("")
      setConfirmPassword("")
      console.log("Google 登录成功")
    } catch (error: any) {
      console.error("Google 登录失败:", error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleGithubLogin = async () => {
    if (isLoading) return // 防止重复点击

    setError("")
    setIsLoading(true)

    try {
      console.log("用户点击 GitHub 登录按钮")
      await onGithubLogin()
      // GitHub 登录成功，清空表单
      setEmail("")
      setPassword("")
      setConfirmPassword("")
      console.log("GitHub 登录成功")
    } catch (error: any) {
      console.error("GitHub 登录失败:", error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleMode = () => {
    setIsLogin(!isLogin)
    setError("")
    setEmail("")
    setPassword("")
    setConfirmPassword("")
  }

  return (
    <div className="auth-container">
      <div className="auth-tabs">
        <button
          className={`tab-button ${isLogin ? 'active' : ''}`}
          onClick={() => setIsLogin(true)}
        >
          登录
        </button>
        <button
          className={`tab-button ${!isLogin ? 'active' : ''}`}
          onClick={() => setIsLogin(false)}
        >
          注册
        </button>
      </div>

      <form onSubmit={handleSubmit} className="auth-form">
        <div className="form-group">
          <label htmlFor="email">邮箱</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="请输入邮箱"
            disabled={isLoading}
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="password">密码</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder={isLogin ? "请输入密码" : "请输入密码（至少6位）"}
            disabled={isLoading}
            required
          />
        </div>

        {!isLogin && (
          <div className="form-group">
            <label htmlFor="confirmPassword">确认密码</label>
            <input
              type="password"
              id="confirmPassword"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="请再次输入密码"
              disabled={isLoading}
              required
            />
          </div>
        )}

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <button
          type="submit"
          className="submit-button"
          disabled={isLoading}
        >
          {isLoading ? "处理中..." : (isLogin ? "登录" : "注册")}
        </button>

        <div className="divider">
          <span>或</span>
        </div>

        <button
          type="button"
          className="google-login-button"
          onClick={handleGoogleLogin}
          disabled={isLoading}
        >
          <Google className="google-icon" />
          {isLoading ? "正在授权..." : `通过 Google 授权${isLogin ? "登录" : "注册"}`}
        </button>

        <button
          type="button"
          className="github-login-button"
          onClick={handleGithubLogin}
          disabled={isLoading}
        >
          <Github className="github-icon" />
          {isLoading ? "正在授权..." : `通过 GitHub 授权${isLogin ? "登录" : "注册"}`}
        </button>
      </form>

      <div className="auth-footer">
        <p>
          {isLogin ? "还没有账户？" : "已有账户？"}
          <button
            type="button"
            className="link-button"
            onClick={toggleMode}
            disabled={isLoading}
          >
            {isLogin ? "立即注册" : "立即登录"}
          </button>
        </p>
      </div>
    </div>
  )
}
