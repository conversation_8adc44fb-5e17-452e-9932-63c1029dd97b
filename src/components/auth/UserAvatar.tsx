import React, { useState, useRef, useEffect } from "react"
import { useFirebaseAuth } from "@/contexts/FirebaseAuthContext"

interface UserAvatarProps {
  className?: string
}

export default function UserAvatar({ className = "" }: UserAvatarProps) {
  const { user, onLogout } = useFirebaseAuth()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleLogout = async () => {
    try {
      await onLogout()
      setIsDropdownOpen(false)
    } catch (error: any) {
      console.error("退出登录失败:", error.message)
      alert("退出登录失败: " + error.message)
    }
  }

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  // 获取用户头像URL
  const getAvatarUrl = () => {
    if (!user) return null

    // 优先使用Firebase用户的photoURL
    if (user.photoURL) {
      return user.photoURL
    }

    // 检查登录提供商
    const googleProvider = user.providerData.find(p => p.providerId === 'google.com')
    const githubProvider = user.providerData.find(p => p.providerId === 'github.com')

    if (googleProvider && googleProvider.photoURL) {
      return googleProvider.photoURL
    }

    if (githubProvider && githubProvider.photoURL) {
      return githubProvider.photoURL
    }

    return null
  }

  // 获取用户显示名称
  const getDisplayName = () => {
    if (!user) return ""
    return user.displayName || user.email || "用户"
  }

  // 获取首字母头像
  const getInitials = () => {
    const name = getDisplayName()
    if (name.includes("@")) {
      // 如果是邮箱，取@前面的第一个字符
      return name.charAt(0).toUpperCase()
    }
    // 如果是名字，取第一个字符
    return name.charAt(0).toUpperCase()
  }

  // 获取登录提供商图标
  const getProviderIcon = () => {
    if (!user) return null

    const googleProvider = user.providerData.find(p => p.providerId === 'google.com')
    const githubProvider = user.providerData.find(p => p.providerId === 'github.com')

    if (googleProvider) return "🔍"
    if (githubProvider) return "🐙"
    return "📧"
  }

  const avatarUrl = getAvatarUrl()

  if (!user) {
    return (
      <div className={`user-avatar-container ${className}`}>
        <button className="login-prompt-btn">
          登录
        </button>
      </div>
    )
  }

  return (
    <div className={`user-avatar-container ${className}`} ref={dropdownRef}>
      <div className="user-avatar" onClick={toggleDropdown}>
        {avatarUrl ? (
          <img
            src={avatarUrl}
            alt="用户头像"
            className="avatar-image"
            onError={(e) => {
              // 如果图片加载失败，隐藏图片显示首字母
              e.currentTarget.style.display = 'none'
            }}
          />
        ) : (
          <div className="avatar-initials">
            {getInitials()}
          </div>
        )}
      </div>

      {isDropdownOpen && (
        <div className="user-dropdown">
          <div className="dropdown-header">
            <div className="user-info-compact">
              {avatarUrl ? (
                <img src={avatarUrl} alt="用户头像" className="dropdown-avatar" />
              ) : (
                <div className="dropdown-avatar-initials">
                  {getInitials()}
                </div>
              )}
              <div className="user-details">
                <div className="user-name">{getDisplayName()}</div>
                <div className="user-email">{user.email}</div>
              </div>
            </div>
          </div>

          <div className="dropdown-divider"></div>

          <div className="dropdown-content">
            <div className="user-info-item">
              <span className="info-label">用户ID:</span>
              <span className="info-value">{user.uid.substring(0, 8)}...</span>
            </div>

            <div className="user-info-item">
              <span className="info-label">登录方式:</span>
              <span className="info-value">
                {getProviderIcon()}
                {user.providerData.find(p => p.providerId === 'google.com') && 'Google'}
                {user.providerData.find(p => p.providerId === 'github.com') && 'GitHub'}
                {user.providerData.find(p => p.providerId === 'password') && '邮箱'}
              </span>
            </div>

            <div className="user-info-item">
              <span className="info-label">邮箱验证:</span>
              <span className={`info-value ${user.emailVerified ? 'verified' : 'not-verified'}`}>
                {user.emailVerified ? '✅ 已验证' : '⚠️ 未验证'}
              </span>
            </div>
          </div>

          <div className="dropdown-divider"></div>

          <div className="dropdown-actions">
            <button className="dropdown-logout-btn" onClick={handleLogout}>
              退出登录
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
