import type { AuthProviderConfig, OAuthTokenResponse } from './types'
import { getAuthConfig } from './config'

// 构建 OAuth 授权 URL
export const buildAuthUrl = (config: AuthProviderConfig): string => {
  const authConfigData = getAuthConfig(config.name as 'google' | 'github')
  const clientId = authConfigData.clientId

  console.log(`Debug: 构建 ${config.displayName} 授权 URL, clientId = ${clientId ? '已设置' : '未设置'}`)

  if (!clientId) {
    throw new Error(`${config.displayName} Client ID 未配置`)
  }

  const redirectUri = chrome.identity.getRedirectURL()
  const state = Math.random().toString(36).substring(2, 15)

  const params = new URLSearchParams({
    client_id: clientId,
    redirect_uri: redirectUri,
    response_type: 'code',
    scope: config.scope,
    state: state
  })

  // Google 特有的参数
  if (config.name === 'google') {
    params.set('prompt', 'select_account')
  }

  return `${config.authUrl}?${params.toString()}`
}

// 启动 OAuth 认证流程
export const launchAuthFlow = (config: AuthProviderConfig): Promise<string> => {
  return new Promise((resolve, reject) => {
    const authUrl = buildAuthUrl(config)
    console.log(`Background: 启动 ${config.displayName} Web Auth Flow:`, authUrl)

    chrome.identity.launchWebAuthFlow({
      url: authUrl,
      interactive: true
    }, (responseUrl) => {
      if (chrome.runtime.lastError) {
        console.error(`Background: ${config.displayName} Web Auth Flow 错误:`, chrome.runtime.lastError)
        reject(new Error(chrome.runtime.lastError.message || `${config.displayName} 授权流程失败`))
        return
      }

      if (!responseUrl) {
        reject(new Error(`用户取消了 ${config.displayName} 授权`))
        return
      }

      console.log(`Background: 收到 ${config.displayName} 重定向 URL:`, responseUrl)

      // 从重定向 URL 中提取授权码
      try {
        const url = new URL(responseUrl)
        const code = url.searchParams.get('code')
        const error = url.searchParams.get('error')

        if (error) {
          reject(new Error(`${config.displayName} 授权失败: ${error}`))
        } else if (code) {
          resolve(code)
        } else {
          reject(new Error(`未收到 ${config.displayName} 授权码`))
        }
      } catch (e) {
        reject(new Error(`解析 ${config.displayName} 重定向 URL 失败`))
      }
    })
  })
}

// 使用授权码交换令牌
export const exchangeCodeForToken = async (
  code: string,
  config: AuthProviderConfig
): Promise<string> => {
  const authConfigData = getAuthConfig(config.name as 'google' | 'github')
  const clientId = authConfigData.clientId
  const clientSecret = authConfigData.clientSecret

  console.log(`Debug: 交换 ${config.displayName} 令牌, clientId = ${clientId ? '已设置' : '未设置'}, clientSecret = ${clientSecret ? '已设置' : '未设置'}`)

  if (!clientId || !clientSecret) {
    throw new Error(`${config.displayName} 认证配置不完整`)
  }
  
  const params = new URLSearchParams({
    client_id: clientId,
    client_secret: clientSecret,
    code: code
  })

  // Google 需要额外的参数
  if (config.name === 'google') {
    params.set('grant_type', 'authorization_code')
    params.set('redirect_uri', chrome.identity.getRedirectURL())
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/x-www-form-urlencoded'
  }

  // GitHub 需要 Accept 头
  if (config.name === 'github') {
    headers['Accept'] = 'application/json'
  }

  const response = await fetch(config.tokenUrl, {
    method: 'POST',
    headers,
    body: params.toString()
  })

  if (!response.ok) {
    const errorData = await response.text()
    throw new Error(`${config.displayName} 令牌交换失败: ${response.status} ${errorData}`)
  }

  const tokenData: OAuthTokenResponse = await response.json()
  console.log(`Background: ${config.displayName} Token response:`, tokenData)

  // 根据配置获取相应的令牌
  const token = config.tokenType === 'id_token' ? tokenData.id_token : tokenData.access_token
  
  if (!token) {
    console.error(`Background: ${config.displayName} Token response missing ${config.tokenType}:`, tokenData)
    throw new Error(`未收到 ${config.displayName} ${config.tokenType === 'id_token' ? 'ID令牌' : '访问令牌'}`)
  }

  return token
}
