import type { AuthProviderConfig, AuthStatus, StoredTokenInfo } from './types'

// 获取认证状态
export const getAuthStatus = (config: AuthProviderConfig): Promise<AuthStatus> => {
  return new Promise((resolve) => {
    chrome.storage.local.get([config.storageKeys.status, config.storageKeys.error], (result) => {
      resolve({
        status: result[config.storageKeys.status] || 'idle',
        error: result[config.storageKeys.error]
      })
    })
  })
}

// 设置认证状态
export const setAuthStatus = (
  config: AuthProviderConfig, 
  status: string, 
  error?: string
): Promise<void> => {
  return new Promise((resolve) => {
    const data: Record<string, any> = {
      [config.storageKeys.status]: status
    }
    
    if (error !== undefined) {
      data[config.storageKeys.error] = error
    }

    chrome.storage.local.set(data, () => {
      resolve()
    })
  })
}

// 存储令牌供 popup 使用
export const storeTokenForPopup = async (
  token: string, 
  config: AuthProviderConfig
): Promise<void> => {
  try {
    console.log(`Background: 存储 ${config.displayName} 令牌供 popup 使用`)

    await new Promise<void>((resolve) => {
      chrome.storage.local.set({
        [config.storageKeys.pendingToken]: token,
        [config.storageKeys.tokenTimestamp]: Date.now()
      }, () => {
        resolve()
      })
    })
  } catch (error: any) {
    console.error(`Background: 存储 ${config.displayName} 令牌失败:`, error)
    throw new Error(`存储 ${config.displayName} 令牌失败: ${error.message}`)
  }
}

// 获取待处理的令牌
export const getPendingToken = (config: AuthProviderConfig): Promise<StoredTokenInfo> => {
  return new Promise((resolve) => {
    chrome.storage.local.get([
      config.storageKeys.pendingToken, 
      config.storageKeys.tokenTimestamp
    ], (result) => {
      if (chrome.runtime.lastError) {
        console.error(`获取待处理 ${config.displayName} 令牌失败:`, chrome.runtime.lastError)
        resolve({ token: null, timestamp: null, isValid: false })
        return
      }

      const token = result[config.storageKeys.pendingToken]
      const timestamp = result[config.storageKeys.tokenTimestamp]

      // 检查 token 是否存在且未过期（5分钟内有效）
      if (token && timestamp) {
        const now = Date.now()
        const tokenAge = now - timestamp
        const maxAge = 5 * 60 * 1000 // 5分钟

        if (tokenAge < maxAge) {
          resolve({ token, timestamp, isValid: true })
        } else {
          console.log(`${config.displayName} 令牌已过期，清除`)
          chrome.storage.local.remove([
            config.storageKeys.pendingToken, 
            config.storageKeys.tokenTimestamp
          ])
          resolve({ token: null, timestamp: null, isValid: false })
        }
      } else {
        resolve({ token: null, timestamp: null, isValid: false })
      }
    })
  })
}

// 清除待处理的令牌
export const clearPendingToken = (config: AuthProviderConfig): Promise<void> => {
  return new Promise((resolve) => {
    chrome.storage.local.remove([
      config.storageKeys.pendingToken, 
      config.storageKeys.tokenTimestamp
    ], () => {
      resolve()
    })
  })
}

// 清除认证状态
export const clearAuthStatus = (config: AuthProviderConfig): Promise<void> => {
  return new Promise((resolve) => {
    chrome.storage.local.remove([
      config.storageKeys.status, 
      config.storageKeys.error
    ], () => {
      resolve()
    })
  })
}

// 清除所有认证相关数据
export const clearAllAuthData = (config: AuthProviderConfig): Promise<void> => {
  return new Promise((resolve) => {
    chrome.storage.local.remove([
      config.storageKeys.status,
      config.storageKeys.error,
      config.storageKeys.pendingToken,
      config.storageKeys.tokenTimestamp
    ], () => {
      resolve()
    })
  })
}
