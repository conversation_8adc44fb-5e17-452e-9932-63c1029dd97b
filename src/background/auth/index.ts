// 导出所有认证相关的类型和函数
export * from './types'
export * from './providers'
export * from './config'
export * from './oauth'
export * from './storage'
export * from './handlers'

// 便捷的导入
export { authProviders, getProviderConfig } from './providers'
export { 
  createAuthHandler, 
  createStatusCheckHandler, 
  createClearStatusHandler,
  createRemoveAuthHandler 
} from './handlers'
export {
  getAuthStatus,
  setAuthStatus,
  storeTokenForPopup,
  getPendingToken,
  clearPendingToken,
  clearAuthStatus,
  clearAllAuthData
} from './storage'
export {
  buildAuthUrl,
  launchAuthFlow,
  exchangeCodeForToken
} from './oauth'
