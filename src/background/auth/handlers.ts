import type { PlasmoMessaging } from "@plasmohq/messaging"
import type { AuthProviderConfig, AuthResult, AuthStatus } from './types'
import { launchAuthFlow, exchangeCodeForToken } from './oauth'
import {
  getAuthStatus,
  setAuthStatus,
  storeTokenForPopup,
  clearAuthStatus,
  clearAllAuthData
} from './storage'

// 创建通用的认证处理器
export const createAuthHandler = (
  config: AuthProviderConfig
): PlasmoMessaging.MessageHandler => {
  return async (req, res) => {
    try {
      console.log(`Background: 收到 ${config.displayName} 认证请求`)

      const result = await handleAuth(config)
      console.log(`Background: ${config.displayName} 认证流程完成`)

      res.send(result)
    } catch (error: any) {
      console.error(`Background: ${config.displayName} 认证流程异常:`, error)
      res.send({ success: false, error: error.message })
    }
  }
}

// 创建通用的状态检查处理器
export const createStatusCheckHandler = (
  config: AuthProviderConfig
): PlasmoMessaging.MessageHandler => {
  return async (req, res) => {
    try {
      console.log(`Background: 收到 ${config.displayName} 认证状态检查请求`)
      
      const result = await getAuthStatus(config)
      
      res.send(result)
    } catch (error: any) {
      console.error(`Background: 检查 ${config.displayName} 认证状态失败:`, error)
      res.send({ status: 'idle', error: error.message })
    }
  }
}

// 创建通用的清除状态处理器
export const createClearStatusHandler = (
  config: AuthProviderConfig
): PlasmoMessaging.MessageHandler => {
  return async (req, res) => {
    try {
      console.log(`Background: 收到清除 ${config.displayName} 认证状态请求`)
      
      await clearAuthStatus(config)
      
      res.send({ success: true })
    } catch (error: any) {
      console.error(`Background: 清除 ${config.displayName} 认证状态失败:`, error)
      res.send({ success: false, error: error.message })
    }
  }
}

// 通用的认证处理逻辑
const handleAuth = async (config: AuthProviderConfig): Promise<AuthResult> => {
  try {
    console.log(`Background: 启动 ${config.displayName} 登录流程...`)

    // 检查是否已经有认证流程在进行中
    const currentStatus = await getAuthStatus(config)

    if (currentStatus.status === 'in_progress') {
      console.log(`Background: ${config.displayName} 认证流程已在进行中，忽略重复请求`)
      return { success: false, error: "认证流程已在进行中" }
    }

    // 设置认证状态为进行中
    await setAuthStatus(config, 'in_progress', null)

    // 使用 launchWebAuthFlow 进行 OAuth 授权
    const authCode = await launchAuthFlow(config)
    console.log(`Background: 获取到 ${config.displayName} 授权码`)

    // 使用授权码交换令牌
    const token = await exchangeCodeForToken(authCode, config)
    console.log(`Background: 成功获取 ${config.displayName} 令牌`)

    // 存储令牌供 popup 使用
    await storeTokenForPopup(token, config)
    console.log(`Background: ${config.displayName} 令牌已存储`)

    // 设置认证状态为 token_ready（表示 token 已准备好，等待 popup 完成认证）
    await setAuthStatus(config, 'token_ready', null)

    return { success: true }
  } catch (error: any) {
    console.error(`Background: ${config.displayName} 登录失败:`, error)

    // 设置认证状态为失败
    await setAuthStatus(config, 'failed', error.message)

    return { success: false, error: error.message }
  }
}

// 通用的清除认证数据处理器（用于 removeAuth）
export const createRemoveAuthHandler = (
  configs: AuthProviderConfig[]
): PlasmoMessaging.MessageHandler => {
  return async (req, res) => {
    try {
      console.log("Background: 收到清除认证请求")

      // 清除 Chrome Identity API 的缓存
      if (chrome.identity && chrome.identity.clearAllCachedAuthTokens) {
        await chrome.identity.clearAllCachedAuthTokens()
        console.log("Background: 已清除 Identity API 缓存")
      }

      // 清除本地存储中的 Firebase 认证信息
      await chrome.storage.local.remove([
        'firebaseToken',
        'firebaseUid',
        'firebaseRefreshToken'
      ])
      console.log("Background: 已清除本地存储的 Firebase 认证信息")

      // 清除所有提供商的认证数据
      for (const config of configs) {
        await clearAllAuthData(config)
      }

      console.log("Background: 认证信息清除成功")
      res.send({ success: true })
    } catch (error: any) {
      console.error("Background: 认证信息清除失败:", error)
      res.send({ success: false, error: error.message })
    }
  }
}
