// 认证配置 - 在构建时从环境变量注入
export const authConfig = {
  google: {
    clientId: process.env.PLASMO_PUBLIC_GOOGLE_CLIENT_ID || "",
    clientSecret: process.env.PLASMO_PUBLIC_GOOGLE_CLIENT_SECRET || ""
  },
  github: {
    clientId: process.env.PLASMO_PUBLIC_GITHUB_CLIENT_ID || "",
    clientSecret: process.env.PLASMO_PUBLIC_GITHUB_CLIENT_SECRET || ""
  }
}

// 获取认证配置
export const getAuthConfig = (provider: 'google' | 'github') => {
  const config = authConfig[provider]

  if (!config.clientId || !config.clientSecret) {
    console.warn(`${provider} 认证配置不完整:`, {
      clientId: config.clientId ? '已设置' : '未设置',
      clientSecret: config.clientSecret ? '已设置' : '未设置'
    })
  }

  return config
}
