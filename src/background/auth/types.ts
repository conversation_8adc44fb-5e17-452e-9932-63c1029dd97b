// 认证提供商类型
export type AuthProviderType = 'google' | 'github'

// 认证提供商配置接口
export interface AuthProviderConfig {
  name: string
  displayName: string
  clientIdEnvKey: string
  clientSecretEnvKey: string
  authUrl: string
  tokenUrl: string
  scope: string
  tokenType: 'id_token' | 'access_token'
  storageKeys: {
    status: string
    error: string
    pendingToken: string
    tokenTimestamp: string
  }
}

// OAuth 响应类型
export interface OAuthTokenResponse {
  access_token?: string
  id_token?: string
  token_type?: string
  expires_in?: number
  refresh_token?: string
  scope?: string
  error?: string
  error_description?: string
}

// 认证结果类型
export interface AuthResult {
  success: boolean
  error?: string
  message?: string
}

// 认证状态类型
export interface AuthStatus {
  status: string
  error?: string
}

// 存储的令牌信息
export interface StoredTokenInfo {
  token: string | null
  timestamp: number | null
  isValid: boolean
}
