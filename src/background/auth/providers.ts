import type { AuthProviderConfig, AuthProviderType } from './types'

// Google 认证提供商配置
const googleConfig: AuthProviderConfig = {
  name: 'google',
  displayName: 'Google',
  clientIdEnvKey: 'PLASMO_PUBLIC_GOOGLE_CLIENT_ID',
  clientSecretEnvKey: 'PLASMO_PUBLIC_GOOGLE_CLIENT_SECRET',
  authUrl: 'https://accounts.google.com/o/oauth2/auth',
  tokenUrl: 'https://oauth2.googleapis.com/token',
  scope: 'openid email profile',
  tokenType: 'id_token',
  storageKeys: {
    status: 'googleAuthStatus',
    error: 'googleAuthError',
    pendingToken: 'pendingIdToken',
    tokenTimestamp: 'idTokenTimestamp'
  }
}

// GitHub 认证提供商配置
const githubConfig: AuthProviderConfig = {
  name: 'github',
  displayName: 'GitHub',
  clientIdEnvKey: 'PLASMO_PUBLIC_GITHUB_CLIENT_ID',
  clientSecretEnvKey: 'PLASMO_PUBLIC_GITHUB_CLIENT_SECRET',
  authUrl: 'https://github.com/login/oauth/authorize',
  tokenUrl: 'https://github.com/login/oauth/access_token',
  scope: 'user:email',
  tokenType: 'access_token',
  storageKeys: {
    status: 'githubAuthStatus',
    error: 'githubAuthError',
    pendingToken: 'pendingGithubToken',
    tokenTimestamp: 'githubTokenTimestamp'
  }
}

// 提供商配置映射
export const authProviders: Record<AuthProviderType, AuthProviderConfig> = {
  google: googleConfig,
  github: githubConfig
}

// 获取提供商配置
export const getProviderConfig = (provider: AuthProviderType): AuthProviderConfig => {
  const config = authProviders[provider]
  if (!config) {
    throw new Error(`不支持的认证提供商: ${provider}`)
  }
  return config
}
