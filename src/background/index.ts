console.log('Hello background!', { id: chrome.runtime.id });

// 存储选择状态，不使用角标
const selectionStates = new Map<number, boolean>();

// 判断是否为普通可用页面（http/https/file）
const isNormalPage = (url?: string) => {
  if (!url) return false;
  return /^https?:\/\//.test(url) || url.startsWith('file://');
};

chrome.action.onClicked.addListener(async (tab) => {
  try {
    const isSelecting = selectionStates.get(tab.id) || false;

    if (!isSelecting) {
      selectionStates.set(tab.id, true);
      chrome.tabs.sendMessage(tab.id, { action: 'startElementSelection' });
    } else {
      selectionStates.set(tab.id, false);
      chrome.tabs.sendMessage(tab.id, { action: 'stopElementSelection' });
    }
  } catch (error) {
    console.error('Error in action click handler:', error);
  }
});

chrome.runtime.onInstalled.addListener(({ reason }) => {
  if (reason === 'install') {
    chrome.storage.local.set({ installDate: Date.now() });
  }

  // 默认禁用所有标签页的action
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id) {
        chrome.action.disable(tab.id);
      }
    });
  });

  chrome.contextMenus.create({
    id: 'css-cliper',
    title: '收藏夹',
    contexts: ['all']
  });
});

// 浏览器启动时默认禁用所有标签页
chrome.runtime.onStartup?.addListener(() => {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id) {
        chrome.action.disable(tab.id);
      }
    });
  });
});

// 新建标签时，默认禁用
chrome.tabs.onCreated.addListener((tab) => {
  if (tab.id) {
    chrome.action.disable(tab.id);
  }
});

// 监听标签页更新：URL可用且加载完成后启用；否则禁用
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && isNormalPage(tab.url)) {
    try {
      await chrome.action.enable(tabId);
    } catch (error) {
      console.error('Error enabling action for tab:', error);
    }
  } else if (changeInfo.status === 'complete' && !isNormalPage(tab.url)) {
    try {
      await chrome.action.disable(tabId);
    } catch (error) {
      console.error('Error disabling action for tab:', error);
    }
  }
});

// 监听标签页激活，确保action状态正确
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    if (isNormalPage(tab.url)) {
      await chrome.action.enable(activeInfo.tabId);
    } else {
      await chrome.action.disable(activeInfo.tabId);
    }
  } catch (error) {
    console.error('Error handling tab activation:', error);
  }
});

// 标签关闭时清理状态
chrome.tabs.onRemoved.addListener((tabId) => {
  selectionStates.delete(tabId);
});

// 监听调试器分离事件
chrome.debugger.onDetach.addListener((source, reason) => {
  console.log('Debugger detached from tab', source.tabId, 'reason:', reason);
});

// 监听调试器事件
chrome.debugger.onEvent.addListener((source, method, params) => {
  // TODO: 处理调试器事件，如 DOM 变化等
  // console.log('Debugger event:', method, params);
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'css-cliper') {
    chrome.tabs.create({
      url: chrome.runtime.getURL('tabs/collection.html')
    });
  }
});

const GOOGLE_ORIGIN = 'https://www.google.com';

// chrome.tabs.onUpdated.addListener(async (tabId, info, tab) => {
//   if (!tab.url) return;
//   const url = new URL(tab.url);
//
//   if (url.origin !== GOOGLE_ORIGIN) {
//     await chrome.sidePanel.setOptions({
//       tabId,
//       path: 'sidepanel.html',
//       enabled: true
//     });
//   } else {
//     await chrome.sidePanel.setOptions({
//       tabId,
//       enabled: false
//     });
//   }
// });

export {};
