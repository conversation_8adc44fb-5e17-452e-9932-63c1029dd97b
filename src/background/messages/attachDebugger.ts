import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const tabId = req.sender?.tab?.id

    if (!tabId) {
      res.send({ success: false, error: 'No tab ID' })
      return
    }

    // 检查是否已经附加了调试器
    const targets = await chrome.debugger.getTargets()
    const isAttached = targets.some(target => target.tabId === tabId && target.attached)
    
    if (!isAttached) {
      await chrome.debugger.attach({ tabId }, '1.3')
      await chrome.debugger.sendCommand({ tabId }, 'Runtime.enable')
      await chrome.debugger.sendCommand({ tabId }, 'DOM.enable')
      await chrome.debugger.sendCommand({ tabId }, 'CSS.enable')
    }
    
    res.send({ success: true, tabId })
  } catch (error: any) {
    console.error('Failed to attach debugger:', error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
