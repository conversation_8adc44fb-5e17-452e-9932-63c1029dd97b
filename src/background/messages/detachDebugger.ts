import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const { tabId: requestTabId } = req.body
    const tabId = req.sender?.tab?.id || requestTabId

    if (!tabId) {
      res.send({ success: false, error: 'No tab ID' })
      return
    }

    await chrome.debugger.detach({ tabId })
    res.send({ success: true })
  } catch (error: any) {
    console.error('Failed to detach debugger:', error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
