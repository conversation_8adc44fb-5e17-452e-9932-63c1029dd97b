import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const { data } = req.body

    if (!data) {
      res.send({ success: false, error: "No data provided" })
      return
    }

    // 执行某些操作
    res.send(data)
  } catch (error: any) {
    console.error("Error in showElementInfo handler:", error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
