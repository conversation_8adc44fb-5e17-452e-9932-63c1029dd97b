import type { PlasmoMessaging } from "@plasmohq/messaging"
import { createClearStatusHandler } from "@/background/auth/handlers"
import { getProviderConfig } from "@/background/auth/providers"
import type { AuthProviderType } from "@/background/auth/types"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const { provider } = req.body as { provider: AuthProviderType }

    if (!provider) {
      res.send({ success: false, error: "缺少 provider 参数" })
      return
    }

    if (provider !== 'google' && provider !== 'github') {
      res.send({ success: false, error: `不支持的认证提供商: ${provider}` })
      return
    }

    console.log(`Background: 收到清除 ${provider} 认证状态请求`)

    const config = getProviderConfig(provider)
    const clearHandler = createClearStatusHandler(config)

    // 调用通用清除状态处理器
    await clearHandler(req, res)
  } catch (error: any) {
    console.error("Background: 清除认证状态异常:", error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
