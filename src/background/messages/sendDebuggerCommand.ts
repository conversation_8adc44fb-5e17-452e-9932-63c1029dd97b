import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const { tabId: requestTabId, method, params } = req.body
    const tabId = requestTabId || req.sender?.tab?.id

    if (!tabId) {
      res.send({ success: false, error: 'No tab ID' })
      return
    }

    if (!method) {
      res.send({ success: false, error: 'No method provided' })
      return
    }

    const result = await chrome.debugger.sendCommand(
      { tabId },
      method,
      params
    )
    
    res.send({ success: true, result })
  } catch (error: any) {
    console.error('Failed to send debugger command:', error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
