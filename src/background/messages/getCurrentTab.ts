import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    res.send({ success: true, tab: tabs[0] })
  } catch (error: any) {
    console.error('Failed to get current tab:', error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
