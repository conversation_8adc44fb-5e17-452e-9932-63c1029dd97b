// Purple flat design theme variables
:root {
  --primary-purple: #8B5CF6;
  --primary-purple-dark: #7C3AED;
  --primary-purple-light: #A78BFA;
  --secondary-purple: #DDD6FE;
  --accent-purple: #F3F4F6;
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;
  --background-primary: #F8F4FF;
  --background-secondary: #FFFFFF;
  --border-color: #E5E7EB;
  --border-hover: #D1D5DB;
  --success-color: #10B981;
  --danger-color: #EF4444;
  --warning-color: #F59E0B;
}

body {
  margin: 0;
}

// Collection page styles
.collection-container {
  min-height: 100vh;
  background: var(--background-primary);
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.collection-header {
  text-align: center;
  margin-bottom: 48px;

  h1 {
    font-size: 2.75rem;
    font-weight: 800;
    color: var(--primary-purple);
    margin: 0 0 12px 0;
    letter-spacing: -0.025em;
  }

  .collection-stats {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin: 0 0 32px 0;
    font-weight: 500;
  }
}

// Search functionality styles
.search-container {
  max-width: 500px;
  margin: 0 auto;

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--background-secondary);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 0 16px;
    transition: all 0.2s ease;

    &:focus-within {
      border-color: var(--primary-purple);
      box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    }

    .search-icon {
      width: 20px;
      height: 20px;
      color: var(--text-muted);
      margin-right: 12px;
      flex-shrink: 0;
    }

    .search-input {
      flex: 1;
      border: none;
      outline: none;
      padding: 16px 0;
      font-size: 1rem;
      color: var(--text-primary);
      background: transparent;

      &::placeholder {
        color: var(--text-muted);
      }
    }

    .search-clear {
      background: none;
      border: none;
      padding: 8px;
      cursor: pointer;
      color: var(--text-muted);
      border-radius: 8px;
      transition: all 0.2s ease;
      margin-left: 8px;

      &:hover {
        background: var(--accent-purple);
        color: var(--text-secondary);
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .search-results {
    margin: 12px 0 0 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
  }
}

// Loading states
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;

  p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin: 24px 0 0 0;
    font-weight: 500;
  }
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--secondary-purple);
  border-top: 4px solid var(--primary-purple);
  border-radius: 50%;
  animation: spin 1s linear infinite;

  &.small {
    width: 24px;
    height: 24px;
    border-width: 3px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  background: var(--primary-purple);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 24px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--primary-purple-dark);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Empty state
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;

  .empty-icon {
    font-size: 5rem;
    margin-bottom: 24px;
    opacity: 0.6;
  }

  h2 {
    font-size: 1.75rem;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    font-weight: 700;
  }

  p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin: 0;
    max-width: 480px;
    line-height: 1.6;
  }
}

// Masonry grid layout
.snippets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  @media (min-width: 1200px) {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

// Snippet card styles
.snippet-card {
  background: var(--background-secondary);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 400px; // 固定高度便于计算比例
  outline: 3px solid transparent;

  &:hover {
    position: relative;
    outline: 3px solid var(--primary-purple);
  }
}

.snippet-preview {
  position: relative;
  height: 80%;
  overflow: hidden;
  padding: 8px;

  .preview-iframe {
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid var(--border-color);
    border-radius: 10px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%278%27 height=%278%27 viewBox=%270 0 8 8%27%3E%3Cg fill=%27%23e2e8f0%27 fill-opacity=%271%27%3E%3Cpath fill-rule=%27evenodd%27 d=%27M0 0h4v4H0V0zm4 4h4v4H4V4z%27/%3E%3C/g%3E%3C/svg%3E");

    &.loaded {
      opacity: 1;
    }
  }

  .preview-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .preview-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--text-muted);

    .error-icon {
      font-size: 2.5rem;
      margin-bottom: 12px;
      opacity: 0.6;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  // 时间戳显示在预览区右下角
  .preview-timestamp {
    position: absolute;
    bottom: 12px;
    right: 12px;
    background: #ABABAB;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(4px);
  }

  // 操作按钮在预览区右下角
  .preview-actions {
    position: absolute;
    bottom: 12px;
    right: 12px;
    display: flex;
    gap: 6px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover .preview-actions {
    opacity: 1;
  }

  &:hover .preview-timestamp {
    opacity: 0;
  }
}

.snippet-content {
  height: 20%;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .snippet-title-container {
    margin-bottom: 4px;

    .snippet-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
      line-height: 1.3;
      cursor: pointer;
      transition: color 0.2s ease;
      word-wrap: break-word;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        color: var(--primary-purple);
      }
    }

    .title-edit-container {
      display: flex;
      align-items: flex-start;
      gap: 6px;

      .title-input {
        flex: 1;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-primary);
        border-radius: 6px;
        padding: 6px 8px;
        background: var(--background-secondary);
        outline: none;
        font-family: inherit;
        cursor: pointer;
        transition: all 0.2s ease;

        // 查看模式：无边框
        &.viewing {
          border: 1px solid transparent;
          cursor: pointer;

          &:hover {
            background: var(--accent-purple);
          }
        }

        // 编辑模式：显示边框
        &.editing {
          border: 1px solid var(--primary-purple);
          cursor: text;

          &:focus {
            border-color: var(--primary-purple-dark);
          }
        }
      }
    }
  }
}

.snippet-description-container {
  .description-edit-container {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .description-input {
      font-size: 0.8rem;
      color: var(--text-secondary);
      border-radius: 6px;
      padding: 6px 8px;
      background: var(--background-secondary);
      outline: none;
      font-family: inherit;
      line-height: 1.4;
      resize: none;
      height: 40px;
      transition: all 0.2s ease;

      // 查看模式：无边框
      &.viewing {
        border: 1px solid transparent;
        cursor: pointer;

        &:hover {
          background: var(--accent-purple);
        }

        &.empty {
          color: var(--text-muted);
          font-style: italic;
        }
      }

      // 编辑模式：显示边框
      &.editing {
        border: 1px solid var(--primary-purple);
        cursor: text;

        &:focus {
          border-color: var(--primary-purple-dark);
        }
      }

      &::placeholder {
        color: var(--text-muted);
      }
    }
  }
}

// 预览区域内的操作按钮样式
.preview-actions .action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);

  &:hover {
    transform: scale(1.1);
  }

  &.codepen-button:hover {
    background: #000000;
  }

  &.codesandbox-button:hover {
    background: #151515;
  }

  &.download-button:hover {
    background: var(--primary-purple);
  }

  &.edit-button:hover {
    background: var(--warning-color);
  }

  &.delete-button:hover {
    background: var(--danger-color);
  }

  svg {
    flex-shrink: 0;
    width: 12px;
    height: 12px;
  }
}

// Load more trigger
.load-more-trigger {
  margin-top: 48px;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
}

// Confirm dialog styles
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 24px;
}

.confirm-dialog {
  background: var(--background-secondary);
  border-radius: 20px;
  border: 1px solid var(--border-color);
  box-shadow: 0 25px 50px rgba(139, 92, 246, 0.25);
  max-width: 420px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.confirm-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0 24px;

  .confirm-dialog-title {
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
  }

  .confirm-dialog-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--accent-purple);
      color: var(--text-secondary);
    }
  }
}

.confirm-dialog-content {
  padding: 24px;
  text-align: center;

  .confirm-dialog-icon {
    margin-bottom: 20px;
    color: var(--danger-color);
  }

  .confirm-dialog-message {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
  }
}

.confirm-dialog-actions {
  display: flex;
  gap: 12px;
  padding: 0 24px 24px 24px;
}

.confirm-dialog-button {
  flex: 1;
  padding: 14px 20px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &.cancel-button {
    background: var(--accent-purple);
    color: var(--text-secondary);
    border-color: var(--border-color);

    &:hover {
      background: var(--secondary-purple);
      border-color: var(--border-hover);
    }
  }

  &.confirm-button {
    background: var(--danger-color);
    color: white;

    &:hover {
      background: #DC2626;
      transform: translateY(-1px);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3);
    }
  }
}
