import React, { createContext, useContext, useEffect, useState } from "react"
import {
  type User,
  onAuthStateChanged,
  signOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendEmailVerification
} from "firebase/auth"
import { Storage } from "@plasmohq/storage"
import { sendToBackground } from "@plasmohq/messaging"

import { auth } from "@/firebase/firebaseClient"
import {
  signInWithProvider,
  signOutProvider,
  requestAuthFromBackground,
  checkAuthStatus,
  clearAuthStatus,
  getPendingToken,
  clearPendingToken
} from "@/firebase/authUtils"

const storage = new Storage()

// Provider 配置接口
interface ProviderConfig {
  name: 'google' | 'github'
  displayName: string
  tokenType: string
  isInProgress: boolean
  setIsInProgress: (value: boolean) => void
}

// Context 类型定义
interface FirebaseAuthContextType {
  user: User | null
  isLoading: boolean
  onLogin: (email: string, password: string) => Promise<User>
  onRegister: (email: string, password: string) => Promise<User>
  onLogout: () => Promise<void>
  onSendVerificationEmail: () => Promise<boolean>
  onGoogleLogin: () => Promise<User>
  onGithubLogin: () => Promise<User>
}

// 创建 Context
const FirebaseAuthContext = createContext<FirebaseAuthContextType | undefined>(undefined)

// Provider 组件
export function FirebaseAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasCheckedPendingAuth, setHasCheckedPendingAuth] = useState(false)
  const [isGoogleAuthInProgress, setIsGoogleAuthInProgress] = useState(false)
  const [isGithubAuthInProgress, setIsGithubAuthInProgress] = useState(false)

  // Provider 配置
  const providerConfigs: Record<'google' | 'github', ProviderConfig> = {
    google: {
      name: 'google',
      displayName: 'Google',
      tokenType: 'ID Token',
      isInProgress: isGoogleAuthInProgress,
      setIsInProgress: setIsGoogleAuthInProgress
    },
    github: {
      name: 'github',
      displayName: 'GitHub',
      tokenType: 'Access Token',
      isInProgress: isGithubAuthInProgress,
      setIsInProgress: setIsGithubAuthInProgress
    }
  }

  // 通用的等待 token 准备函数
  const waitForTokenReady = async (providerName: 'google' | 'github', tokenType: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(async () => {
        try {
          const authStatus = await checkAuthStatus(providerName)

          if (authStatus.status === 'token_ready') {
            // Token 已准备好，获取并使用
            const token = await getPendingToken(providerName)
            if (token) {
              clearInterval(checkInterval)
              await clearPendingToken(providerName)
              const user = await signInWithProvider(providerName, token)
              await clearAuthStatus(providerName)
              console.log(`${providerName} 认证完成:`, user.email)
              resolve(user)
            } else {
              // Token 不存在，可能已过期
              clearInterval(checkInterval)
              await clearAuthStatus(providerName)
              reject(new Error(`${tokenType} 不存在或已过期`))
            }
          } else if (authStatus.status === 'failed') {
            clearInterval(checkInterval)
            await clearAuthStatus(providerName)
            reject(new Error(authStatus.error || `${providerName} 认证失败`))
          }
          // 如果状态是 'in_progress'，继续等待
        } catch (error) {
          clearInterval(checkInterval)
          reject(error)
        }
      }, 1000) // 每秒检查一次

      // 设置超时（30秒）
      setTimeout(() => {
        clearInterval(checkInterval)
        reject(new Error(`${providerName} 认证超时`))
      }, 30000)
    })
  }

  // 通用的待处理认证处理函数
  const processPendingAuth = async (providerName: 'google' | 'github', tokenType: string): Promise<boolean> => {
    try {
      const pendingToken = await getPendingToken(providerName)
      if (pendingToken && !user) {
        console.log(`发现待处理的 ${tokenType}，正在恢复认证...`)
        try {
          await clearPendingToken(providerName)
          await signInWithProvider(providerName, pendingToken)
          await clearAuthStatus(providerName)
          console.log(`${providerName} 认证恢复成功`)
          return true
        } catch (error) {
          console.error(`${providerName} 认证恢复失败:`, error)
          await clearAuthStatus(providerName)
        }
      }
      return false
    } catch (error) {
      console.error(`处理 ${providerName} 待处理认证失败:`, error)
      return false
    }
  }

  // 通用的 OAuth 登录函数
  const handleOAuthLogin = async (config: ProviderConfig) => {
    if (config.isInProgress) {
      console.log(`${config.displayName} 认证已在进行中，忽略重复请求`)
      return
    }

    try {
      config.setIsInProgress(true)

      // 首先检查是否有待处理的 Token
      const pendingToken = await getPendingToken(config.name)
      if (pendingToken) {
        console.log(`发现待处理的 ${config.tokenType}，直接使用`)
        await clearPendingToken(config.name)
        const user = await signInWithProvider(config.name, pendingToken)
        await clearAuthStatus(config.name)
        return user
      }

      // 检查认证状态
      const authStatus = await checkAuthStatus(config.name)

      if (authStatus.status === 'in_progress') {
        // 如果认证正在进行中，等待完成
        return await waitForTokenReady(config.name, config.tokenType)
      } else if (authStatus.status === 'token_ready') {
        // 如果 token 已准备好，获取并使用
        const token = await getPendingToken(config.name)
        if (token) {
          await clearPendingToken(config.name)
          const user = await signInWithProvider(config.name, token)
          await clearAuthStatus(config.name)
          return user
        }
      } else if (authStatus.status === 'failed') {
        // 如果之前的认证失败了，清除状态并重新开始
        await clearAuthStatus(config.name)
      }

      // 启动新的认证流程
      console.log(`启动新的 ${config.displayName} 认证流程`)
      await requestAuthFromBackground(config.name)

      // 等待 token 准备好
      return await waitForTokenReady(config.name, config.tokenType)
    } catch (error: any) {
      throw new Error(getErrorMessage(error.code))
    } finally {
      config.setIsInProgress(false)
    }
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        // 用户已登录，保存用户信息到存储
        await storage.set("firebaseToken", await user.getIdToken())
        await storage.set("firebaseUid", user.uid)
        await storage.set("firebaseRefreshToken", user.refreshToken)
        setUser(user)
      } else {
        // 用户未登录，清除存储的用户信息
        await storage.set("firebaseToken", null)
        await storage.set("firebaseUid", null)
        await storage.set("firebaseRefreshToken", null)
        setUser(null)
      }
      setIsLoading(false)
    })

    return () => unsubscribe()
  }, [])

  // 单独的 useEffect 来处理待处理的认证，只在初始化时执行一次
  useEffect(() => {
    if (!hasCheckedPendingAuth && !isLoading) {
      const checkPendingAuth = async () => {
        try {
          // 检查 Google 和 GitHub 的待处理认证
          const googleProcessed = await processPendingAuth('google', 'Google ID Token')
          if (googleProcessed) {
            setHasCheckedPendingAuth(true)
            return
          }

          const githubProcessed = await processPendingAuth('github', 'GitHub Access Token')
          if (githubProcessed) {
            setHasCheckedPendingAuth(true)
            return
          }

          setHasCheckedPendingAuth(true)
        } catch (error) {
          console.error("检查待处理认证失败:", error)
          setHasCheckedPendingAuth(true)
        }
      }

      checkPendingAuth()
    }
  }, [hasCheckedPendingAuth, isLoading, user])

  const onLogin = async (email: string, password: string) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      return userCredential.user
    } catch (error: any) {
      throw new Error(getErrorMessage(error.code))
    }
  }

  const onRegister = async (email: string, password: string) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)

      // 发送邮箱验证邮件
      await sendEmailVerification(userCredential.user, {
        url: 'https://css-cliper.web.app', // 验证后跳转的URL
        handleCodeInApp: false
      })

      return userCredential.user
    } catch (error: any) {
      throw new Error(getErrorMessage(error.code))
    }
  }

  const onLogout = async () => {
    try {
      // 清除所有 OAuth provider 的认证缓存
      const providerMappings = {
        'google.com': 'google',
        'github.com': 'github'
      } as const

      for (const [providerId, providerName] of Object.entries(providerMappings)) {
        if (user?.providerData.some(provider => provider.providerId === providerId)) {
          await signOutProvider(providerName as 'google' | 'github')
        }
      }

      await signOut(auth)
      // 通过后台脚本清除存储的认证信息
      await sendToBackground({
        name: "removeAuth"
      })
    } catch (error: any) {
      throw new Error("退出登录失败")
    }
  }

  const onSendVerificationEmail = async () => {
    try {
      if (user && !user.emailVerified) {
        await sendEmailVerification(user, {
          url: 'https://css-cliper.web.app',
          handleCodeInApp: false
        })
        return true
      }
      return false
    } catch (error: any) {
      throw new Error("发送验证邮件失败")
    }
  }

  const onGoogleLogin = async () => {
    return await handleOAuthLogin(providerConfigs.google)
  }

  const onGithubLogin = async () => {
    return await handleOAuthLogin(providerConfigs.github)
  }

  const value: FirebaseAuthContextType = {
    user,
    isLoading,
    onLogin,
    onRegister,
    onLogout,
    onSendVerificationEmail,
    onGoogleLogin,
    onGithubLogin
  }

  return (
    <FirebaseAuthContext.Provider value={value}>
      {children}
    </FirebaseAuthContext.Provider>
  )
}

// 自定义 hook
export function useFirebaseAuth() {
  const context = useContext(FirebaseAuthContext)
  if (context === undefined) {
    throw new Error('useFirebaseAuth must be used within a FirebaseAuthProvider')
  }
  return context
}

// 错误消息映射
function getErrorMessage(errorCode: string): string {
  const errorMessages: { [key: string]: string } = {
    'auth/email-already-in-use': '邮箱已被使用',
    'auth/invalid-email': '邮箱格式不正确',
    'auth/operation-not-allowed': '操作不被允许',
    'auth/weak-password': '密码强度不够（至少6位）',
    'auth/user-disabled': '用户账户已被禁用',
    'auth/user-not-found': '用户不存在',
    'auth/wrong-password': '密码错误',
    'auth/invalid-credential': '登录凭据无效',
    'auth/too-many-requests': '请求过于频繁，请稍后再试',
    'auth/network-request-failed': '网络连接失败',
    'auth/missing-password': '请输入密码'
  }

  return errorMessages[errorCode] || '操作失败，请重试'
}
