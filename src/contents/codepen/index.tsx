import React from 'react';
import ExtensionErrorBoundary from '@/components/ExtensionErrorBoundary.tsx';
import Panel from '@/components/Panel';
import type { PlasmoCSConfig, PlasmoGetOverlayAnchor, PlasmoGetStyle } from 'plasmo';
import { checkAndHandleContextInvalidation } from '@/utils/extension-utils';
import cssText from 'data-text:./codepen.scss';

export const config: PlasmoCSConfig = {
  matches: ['<all_urls>'],
  all_frames: false
};

// 注入样式
export const getStyle: PlasmoGetStyle = () => {
  const style = document.createElement('style');
  style.textContent = cssText;
  return style;
};

export const getShadowHostId = () => 'css-cliper';

export const getOverlayAnchor: PlasmoGetOverlayAnchor = async () => document.body;

// 全局错误处理
window.addEventListener('error', (event) => {
  if (event.error?.message?.includes('Extension context invalidated')) {
    console.warn('全局错误处理器捕获到扩展上下文失效');
    checkAndHandleContextInvalidation();
    event.preventDefault();
  }
});

// 全局未处理的Promise拒绝处理
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason?.message?.includes('Extension context invalidated') ||
      event.reason?.message?.includes('Could not establish connection')) {
    console.warn('全局Promise拒绝处理器捕获到扩展上下文失效');
    checkAndHandleContextInvalidation();
    event.preventDefault();
  }
});

export default () => {
  return (
    <ExtensionErrorBoundary>
      <Panel />
    </ExtensionErrorBoundary>
  );
};
