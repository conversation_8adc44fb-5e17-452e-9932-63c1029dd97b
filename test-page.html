<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Cliper 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>CSS Cliper 测试页面</h1>
        <p>这是一个用于测试 CSS Cliper 扩展功能的页面</p>
    </div>
    
    <div class="content">
        <h2>功能说明</h2>
        <p>在这个页面上，你可以测试以下功能：</p>
        <ul>
            <li>插件图标应该默认为激活状态（因为这是普通网页）</li>
            <li>点击插件图标开始元素选择模式</li>
            <li>再次点击插件图标退出选择模式</li>
            <li>不会显示右下角角标</li>
        </ul>
    </div>
    
    <div class="card">
        <h3>示例卡片 1</h3>
        <p>这是一个带有样式的卡片，你可以用 CSS Cliper 来选择它</p>
        <button class="button">示例按钮</button>
    </div>
    
    <div class="card">
        <h3>示例卡片 2</h3>
        <p>另一个带有不同内容的卡片</p>
        <button class="button">另一个按钮</button>
    </div>
    
    <div class="content">
        <h2>测试说明</h2>
        <ol>
            <li>安装扩展后，访问此页面</li>
            <li>检查扩展图标是否为激活状态（不是灰色）</li>
            <li>点击扩展图标应该进入元素选择模式</li>
            <li>检查是否没有显示角标</li>
            <li>按ESC键或再次点击图标退出选择模式</li>
        </ol>
    </div>
</body>
</html>